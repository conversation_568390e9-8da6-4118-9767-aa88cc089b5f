******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Jun 27 12:02:53 2025

OUTPUT FILE NAME:   <empty.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 00000b55


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  000014d0  0001eb30  R  X
  FLASH                 00000000   00020000  00000000  00020000  R  X
  SRAM                  20200000   00008000  00000222  00007dde  RW X
  SRAM                  20200000   00008000  00000000  00008000  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    000014d0   000014d0    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000ba8   00000ba8    r-x .text
  00000c68    00000c68    00000830   00000830    r-- .rodata
  00001498    00001498    00000038   00000038    r-- .cinit
20200000    20200000    00000025   00000000    rw-
  20200000    20200000    0000001d   00000000    rw- .data
  20200020    20200020    00000005   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000ba8     
                  000000c0    000002b8     oled_hardware_i2c.o (.text.OLED_Init)
                  00000378    00000144     oled_hardware_i2c.o (.text.OLED_Clear)
                  000004bc    00000134     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  000005f0    000000dc     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  000006cc    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000766    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000768    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000007e4    0000007a     oled_hardware_i2c.o (.text.OLED_ShowString)
                  0000085e    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  000008bc    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  00000910    00000044     scheduler.o (.text.scheduler_run)
                  00000954    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000994    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000009d4    0000003c     empty.o (.text.main)
                  00000a10    0000003c     oled_app.o (.text.oled_task)
                  00000a4c    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  00000a88    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000abc    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000af0    00000034     clock.o (.text.mspm0_delay_ms)
                  00000b24    00000030     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSTICK_init)
                  00000b54    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  00000b7c    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  00000ba2    00000018     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000bba    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000bbc    00000018     clock.o (.text.mspm0_get_clock_ms)
                  00000bd4    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00000bea    00000002     --HOLE-- [fill = 0]
                  00000bec    00000014     scheduler.o (.text.scheduler_init)
                  00000c00    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00000c12    00000002     --HOLE-- [fill = 0]
                  00000c14    00000010     interrupt.o (.text.SysTick_Handler)
                  00000c24    00000010     clock.o (.text.SysTick_Init)
                  00000c34    0000000c     empty.o (.text.led_blink_task)
                  00000c40    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00000c4a    00000002     --HOLE-- [fill = 0]
                  00000c4c    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00000c54    00000006     libc.a : exit.c.obj (.text:abort)
                  00000c5a    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00000c5e    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000c62    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00000c66    00000002     --HOLE-- [fill = 0]

.cinit     0    00001498    00000038     
                  00001498    00000013     (.cinit..data.load) [load image, compression = lzss]
                  000014ab    00000001     --HOLE-- [fill = 0]
                  000014ac    0000000c     (__TI_handler_table)
                  000014b8    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  000014c0    00000010     (__TI_cinit_table)

.rodata    0    00000c68    00000830     
                  00000c68    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  00001258    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  00001480    0000000c     oled_app.o (.rodata.str1.17288060079586621852.1)
                  0000148c    00000009     oled_app.o (.rodata.str1.865768355259620247.1)
                  00001495    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  00001497    00000001     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    0000001d     UNINITIALIZED
                  20200000    00000018     scheduler.o (.data.scheduler_task)
                  20200018    00000004     clock.o (.data.sys_tick)
                  2020001c    00000001     oled_app.o (.data.oled_init_done)

.bss       0    20200020    00000005     UNINITIALIZED
                  20200020    00000004     (.common:start_time)
                  20200024    00000001     (.common:task_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

.cinit     0    00000000    00000000     UNINITIALIZED

.init_array 
*          0    00000000    00000000     UNINITIALIZED

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             324    2         0      
       startup_mspm0g350x_ticlang.o   6      192       0      
       scheduler.o                    88     0         25     
       empty.o                        72     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         490    194       25     
                                                              
    .\APP\
       oled_app.o                     60     21        1      
    +--+------------------------------+------+---------+---------+
       Total:                         60     21        1      
                                                              
    .\BSP\OLED\
       oled_hardware_i2c.o            1670   2072      0      
    +--+------------------------------+------+---------+---------+
       Total:                         1670   2072      0      
                                                              
    .\System\
       clock.o                        92     0         8      
       interrupt.o                    16     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         108    0         8      
                                                              
    D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_i2c.o                       132    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         142    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_uidivmod.S.obj           64     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         74     0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      55        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   2976   2342      546    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 000014c0 records: 2, size/record: 8, table size: 16
	.data: load addr=00001498, load size=00000013 bytes, run addr=20200000, run size=0000001d bytes, compression=lzss
	.bss: load addr=000014b8, load size=00000008 bytes, run addr=20200020, run size=00000005 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 000014ac records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
00000767  ADC0_IRQHandler               
00000767  ADC1_IRQHandler               
00000767  AES_IRQHandler                
00000c5a  C$$EXIT                       
00000767  CANFD0_IRQHandler             
00000767  DAC0_IRQHandler               
00000c41  DL_Common_delayCycles         
0000085f  DL_I2C_fillControllerTXFIFO   
00000b7d  DL_I2C_setClockConfig         
00000767  DMA_IRQHandler                
00000767  Default_Handler               
00000767  GROUP0_IRQHandler             
00000767  GROUP1_IRQHandler             
00000c5b  HOSTexit                      
00000767  HardFault_Handler             
00000767  I2C0_IRQHandler               
00000767  I2C1_IRQHandler               
00000767  NMI_Handler                   
00000379  OLED_Clear                    
000000c1  OLED_Init                     
000005f1  OLED_ShowChar                 
000007e5  OLED_ShowString               
000004bd  OLED_WR_Byte                  
00000767  PendSV_Handler                
00000767  RTC_IRQHandler                
00000c5f  Reset_Handler                 
00000767  SPI0_IRQHandler               
00000767  SPI1_IRQHandler               
00000767  SVC_Handler                   
00000a89  SYSCFG_DL_GPIO_init           
000008bd  SYSCFG_DL_I2C_OLED_init       
00000955  SYSCFG_DL_SYSCTL_init         
00000b25  SYSCFG_DL_SYSTICK_init        
00000ba3  SYSCFG_DL_init                
00000abd  SYSCFG_DL_initPower           
00000c15  SysTick_Handler               
00000c25  SysTick_Init                  
00000767  TIMA0_IRQHandler              
00000767  TIMA1_IRQHandler              
00000767  TIMG0_IRQHandler              
00000767  TIMG12_IRQHandler             
00000767  TIMG6_IRQHandler              
00000767  TIMG7_IRQHandler              
00000767  TIMG8_IRQHandler              
00000767  UART0_IRQHandler              
00000767  UART1_IRQHandler              
00000767  UART2_IRQHandler              
00000767  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
000014c0  __TI_CINIT_Base               
000014d0  __TI_CINIT_Limit              
000014d0  __TI_CINIT_Warm               
000014ac  __TI_Handler_Table_Base       
000014b8  __TI_Handler_Table_Limit      
00000a4d  __TI_auto_init_nobinit_nopinit
00000769  __TI_decompress_lzss          
00000c01  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00000bd5  __TI_zero_init_nomemset       
00000bbb  __aeabi_idiv0                 
00000c4d  __aeabi_memcpy                
00000c4d  __aeabi_memcpy4               
00000c4d  __aeabi_memcpy8               
00000995  __aeabi_uidiv                 
00000995  __aeabi_uidivmod              
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
00000b55  _c_int00_noargs               
UNDEFED   _system_post_cinit            
00000c63  _system_pre_init              
00000c55  abort                         
00001258  asc2_0806                     
00000c68  asc2_1608                     
ffffffff  binit                         
00000000  interruptVectors              
00000c35  led_blink_task                
000009d5  main                          
000006cd  memcpy                        
00000af1  mspm0_delay_ms                
00000bbd  mspm0_get_clock_ms            
00000a11  oled_task                     
00000bed  scheduler_init                
00000911  scheduler_run                 
20200020  start_time                    
20200018  sys_tick                      
20200024  task_num                      


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  OLED_Init                     
00000200  __STACK_SIZE                  
00000379  OLED_Clear                    
000004bd  OLED_WR_Byte                  
000005f1  OLED_ShowChar                 
000006cd  memcpy                        
00000767  ADC0_IRQHandler               
00000767  ADC1_IRQHandler               
00000767  AES_IRQHandler                
00000767  CANFD0_IRQHandler             
00000767  DAC0_IRQHandler               
00000767  DMA_IRQHandler                
00000767  Default_Handler               
00000767  GROUP0_IRQHandler             
00000767  GROUP1_IRQHandler             
00000767  HardFault_Handler             
00000767  I2C0_IRQHandler               
00000767  I2C1_IRQHandler               
00000767  NMI_Handler                   
00000767  PendSV_Handler                
00000767  RTC_IRQHandler                
00000767  SPI0_IRQHandler               
00000767  SPI1_IRQHandler               
00000767  SVC_Handler                   
00000767  TIMA0_IRQHandler              
00000767  TIMA1_IRQHandler              
00000767  TIMG0_IRQHandler              
00000767  TIMG12_IRQHandler             
00000767  TIMG6_IRQHandler              
00000767  TIMG7_IRQHandler              
00000767  TIMG8_IRQHandler              
00000767  UART0_IRQHandler              
00000767  UART1_IRQHandler              
00000767  UART2_IRQHandler              
00000767  UART3_IRQHandler              
00000769  __TI_decompress_lzss          
000007e5  OLED_ShowString               
0000085f  DL_I2C_fillControllerTXFIFO   
000008bd  SYSCFG_DL_I2C_OLED_init       
00000911  scheduler_run                 
00000955  SYSCFG_DL_SYSCTL_init         
00000995  __aeabi_uidiv                 
00000995  __aeabi_uidivmod              
000009d5  main                          
00000a11  oled_task                     
00000a4d  __TI_auto_init_nobinit_nopinit
00000a89  SYSCFG_DL_GPIO_init           
00000abd  SYSCFG_DL_initPower           
00000af1  mspm0_delay_ms                
00000b25  SYSCFG_DL_SYSTICK_init        
00000b55  _c_int00_noargs               
00000b7d  DL_I2C_setClockConfig         
00000ba3  SYSCFG_DL_init                
00000bbb  __aeabi_idiv0                 
00000bbd  mspm0_get_clock_ms            
00000bd5  __TI_zero_init_nomemset       
00000bed  scheduler_init                
00000c01  __TI_decompress_none          
00000c15  SysTick_Handler               
00000c25  SysTick_Init                  
00000c35  led_blink_task                
00000c41  DL_Common_delayCycles         
00000c4d  __aeabi_memcpy                
00000c4d  __aeabi_memcpy4               
00000c4d  __aeabi_memcpy8               
00000c55  abort                         
00000c5a  C$$EXIT                       
00000c5b  HOSTexit                      
00000c5f  Reset_Handler                 
00000c63  _system_pre_init              
00000c68  asc2_1608                     
00001258  asc2_0806                     
000014ac  __TI_Handler_Table_Base       
000014b8  __TI_Handler_Table_Limit      
000014c0  __TI_CINIT_Base               
000014d0  __TI_CINIT_Limit              
000014d0  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200018  sys_tick                      
20200020  start_time                    
20200024  task_num                      
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[104 symbols]
