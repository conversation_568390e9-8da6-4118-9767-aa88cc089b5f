******************************************************************************
            TI ARM Clang Linker PC v4.0.3                      
******************************************************************************
>> Linked Fri Jun 27 00:59:52 2025

OUTPUT FILE NAME:   <empty.out>
ENTRY POINT SYMBOL: "_c_int00_noargs"  address: 000009a9


MEMORY CONFIGURATION

         name            origin    length      used     unused   attr    fill
----------------------  --------  ---------  --------  --------  ----  --------
  FLASH                 00000000   00020000  00001340  0001ecc0  R  X
  SRAM                  20200000   00008000  00000221  00007ddf  RW X
  BCR_CONFIG            41c00000   000000ff  00000000  000000ff  R   
  BSL_CONFIG            41c00100   00000080  00000000  00000080  R   


SEGMENT ALLOCATION MAP

run origin  load origin   length   init length attrs members
----------  ----------- ---------- ----------- ----- -------
00000000    00000000    00001340   00001340    r-x
  00000000    00000000    000000c0   000000c0    r-- .intvecs
  000000c0    000000c0    00000a18   00000a18    r-x .text
  00000ad8    00000ad8    00000828   00000828    r-- .rodata
  00001300    00001300    00000040   00000040    r-- .cinit
20200000    20200000    00000021   00000000    rw-
  20200000    20200000    0000001c   00000000    rw- .data
  2020001c    2020001c    00000005   00000000    rw- .bss
20207e00    20207e00    00000200   00000000    rw-
  20207e00    20207e00    00000200   00000000    rw- .stack


SECTION ALLOCATION MAP

 output                                  attributes/
section   page    origin      length       input sections
--------  ----  ----------  ----------   ----------------
.intvecs   0    00000000    000000c0     
                  00000000    000000c0     startup_mspm0g350x_ticlang.o (.intvecs)

.text      0    000000c0    00000a18     
                  000000c0    000002b8     oled_hardware_i2c.o (.text.OLED_Init)
                  00000378    00000134     oled_hardware_i2c.o (.text.OLED_WR_Byte)
                  000004ac    000000dc     oled_hardware_i2c.o (.text.OLED_ShowChar)
                  00000588    0000009a     libc.a : memcpy16.S.obj (.text:memcpy)
                  00000622    00000002     startup_mspm0g350x_ticlang.o (.text.Default_Handler)
                  00000624    0000007c     libc.a : copy_decompress_lzss.c.obj (.text:decompress:lzss)
                  000006a0    0000007a     oled_hardware_i2c.o (.text.OLED_ShowString)
                  0000071a    0000005e     driverlib.a : dl_i2c.o (.text.DL_I2C_fillControllerTXFIFO)
                  00000778    00000054     ti_msp_dl_config.o (.text.SYSCFG_DL_I2C_OLED_init)
                  000007cc    00000044     scheduler.o (.text.scheduler_run)
                  00000810    00000040     ti_msp_dl_config.o (.text.SYSCFG_DL_SYSCTL_init)
                  00000850    00000040     clock.o (.text.SysTick_Init)
                  00000890    00000040     libclang_rt.builtins.a : aeabi_uidivmod.S.obj (.text.__aeabi_uidivmod)
                  000008d0    0000003c     libc.a : autoinit.c.obj (.text:__TI_auto_init_nobinit_nopinit)
                  0000090c    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_GPIO_init)
                  00000940    00000034     ti_msp_dl_config.o (.text.SYSCFG_DL_initPower)
                  00000974    00000034     clock.o (.text.mspm0_delay_ms)
                  000009a8    00000028     libc.a : boot_cortex_m.c.obj (.text:_c_int00_noargs)
                  000009d0    00000026     driverlib.a : dl_i2c.o (.text.DL_I2C_setClockConfig)
                  000009f6    0000001a     empty.o (.text.main)
                  00000a10    00000018     clock.o (.text.mspm0_get_clock_ms)
                  00000a28    00000016     libc.a : copy_zero_init.c.obj (.text:decompress:ZI:__TI_zero_init_nomemset)
                  00000a3e    00000014     ti_msp_dl_config.o (.text.SYSCFG_DL_init)
                  00000a52    00000002     libclang_rt.builtins.a : aeabi_div0.c.obj (.text.__aeabi_idiv0)
                  00000a54    00000014     oled_app.o (.text.oled_task)
                  00000a68    00000014     scheduler.o (.text.scheduler_init)
                  00000a7c    00000012     libc.a : copy_decompress_none.c.obj (.text:decompress:none)
                  00000a8e    00000002     --HOLE-- [fill = 0]
                  00000a90    00000010     interrupt.o (.text.SysTick_Handler)
                  00000aa0    0000000c     empty.o (.text.led_blink_task)
                  00000aac    0000000a     driverlib.a : dl_common.o (.text.DL_Common_delayCycles)
                  00000ab6    00000002     --HOLE-- [fill = 0]
                  00000ab8    00000008     libclang_rt.builtins.a : aeabi_memcpy.S.obj (.text.__aeabi_memcpy)
                  00000ac0    00000006     libc.a : exit.c.obj (.text:abort)
                  00000ac6    00000004     libsysbm.a : hostexit.c.obj (.text.HOSTexit)
                  00000aca    00000004     startup_mspm0g350x_ticlang.o (.text.Reset_Handler)
                  00000ace    00000004     libc.a : pre_init.c.obj (.text._system_pre_init)
                  00000ad2    00000006     --HOLE-- [fill = 0]

.cinit     0    00001300    00000040     
                  00001300    00000016     (.cinit..data.load) [load image, compression = lzss]
                  00001316    00000002     --HOLE-- [fill = 0]
                  00001318    0000000c     (__TI_handler_table)
                  00001324    00000008     (.cinit..bss.load) [load image, compression = zero_init]
                  0000132c    00000010     (__TI_cinit_table)
                  0000133c    00000004     --HOLE-- [fill = 0]

.rodata    0    00000ad8    00000828     
                  00000ad8    000005f0     oled_hardware_i2c.o (.rodata.asc2_1608)
                  000010c8    00000228     oled_hardware_i2c.o (.rodata.asc2_0806)
                  000012f0    00000009     oled_app.o (.rodata.str1.17288060079586621852.1)
                  000012f9    00000002     ti_msp_dl_config.o (.rodata.gI2C_OLEDClockConfig)
                  000012fb    00000005     --HOLE-- [fill = 0]

.init_array 
*          0    00000000    00000000     UNINITIALIZED

.binit     0    00000000    00000000     

.data      0    20200000    0000001c     UNINITIALIZED
                  20200000    00000018     scheduler.o (.data.scheduler_task)
                  20200018    00000004     clock.o (.data.sys_tick)

.bss       0    2020001c    00000005     UNINITIALIZED
                  2020001c    00000004     (.common:start_time)
                  20200020    00000001     (.common:task_num)

.stack     0    20207e00    00000200     UNINITIALIZED
                  20207e00    00000004     libc.a : boot_cortex_m.c.obj (.stack)
                  20207e04    000001fc     --HOLE--

__llvm_prf_cnts 
*          0    20200000    00000000     UNINITIALIZED

__llvm_prf_bits 
*          0    20200000    00000000     UNINITIALIZED

MODULE SUMMARY

       Module                         code   ro data   rw data
       ------                         ----   -------   -------
    .\
       ti_msp_dl_config.o             272    2         0      
       startup_mspm0g350x_ticlang.o   6      192       0      
       scheduler.o                    88     0         25     
       empty.o                        38     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         404    194       25     
                                                              
    .\APP\
       oled_app.o                     20     9         0      
    +--+------------------------------+------+---------+---------+
       Total:                         20     9         0      
                                                              
    .\BSP\OLED\
       oled_hardware_i2c.o            1346   2072      0      
    +--+------------------------------+------+---------+---------+
       Total:                         1346   2072      0      
                                                              
    .\System\
       clock.o                        140    0         8      
       interrupt.o                    16     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         156    0         8      
                                                              
    D:/TI/CCS/mspm0_sdk_2_05_00_05/source/ti/driverlib/lib/ticlang/m0p/mspm0g1x0x_g3x0x/driverlib.a
       dl_i2c.o                       132    0         0      
       dl_common.o                    10     0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         142    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libc.a
       memcpy16.S.obj                 154    0         0      
       copy_decompress_lzss.c.obj     124    0         0      
       autoinit.c.obj                 60     0         0      
       boot_cortex_m.c.obj            40     0         0      
       copy_zero_init.c.obj           22     0         0      
       copy_decompress_none.c.obj     18     0         0      
       exit.c.obj                     6      0         0      
       pre_init.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         428    0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/c/libsysbm.a
       hostexit.c.obj                 4      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         4      0         0      
                                                              
    D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi/libclang_rt.builtins.a
       aeabi_uidivmod.S.obj           64     0         0      
       aeabi_memcpy.S.obj             8      0         0      
       aeabi_div0.c.obj               2      0         0      
    +--+------------------------------+------+---------+---------+
       Total:                         74     0         0      
                                                              
       Stack:                         0      0         512    
       Linker Generated:              0      58        0      
    +--+------------------------------+------+---------+---------+
       Grand Total:                   2574   2333      545    


LINKER GENERATED COPY TABLES

__TI_cinit_table @ 0000132c records: 2, size/record: 8, table size: 16
	.data: load addr=00001300, load size=00000016 bytes, run addr=20200000, run size=0000001c bytes, compression=lzss
	.bss: load addr=00001324, load size=00000008 bytes, run addr=2020001c, run size=00000005 bytes, compression=zero_init


LINKER GENERATED HANDLER TABLE

__TI_handler_table @ 00001318 records: 3, size/record: 4, table size: 12
	index: 0, handler: __TI_decompress_lzss
	index: 1, handler: __TI_decompress_none
	index: 2, handler: __TI_zero_init


GLOBAL SYMBOLS: SORTED ALPHABETICALLY BY Name 

address   name                          
-------   ----                          
00000623  ADC0_IRQHandler               
00000623  ADC1_IRQHandler               
00000623  AES_IRQHandler                
00000ac6  C$$EXIT                       
00000623  CANFD0_IRQHandler             
00000623  DAC0_IRQHandler               
00000aad  DL_Common_delayCycles         
0000071b  DL_I2C_fillControllerTXFIFO   
000009d1  DL_I2C_setClockConfig         
00000623  DMA_IRQHandler                
00000623  Default_Handler               
00000623  GROUP0_IRQHandler             
00000623  GROUP1_IRQHandler             
00000ac7  HOSTexit                      
00000623  HardFault_Handler             
00000623  I2C0_IRQHandler               
00000623  I2C1_IRQHandler               
00000623  NMI_Handler                   
000000c1  OLED_Init                     
000004ad  OLED_ShowChar                 
000006a1  OLED_ShowString               
00000379  OLED_WR_Byte                  
00000623  PendSV_Handler                
00000623  RTC_IRQHandler                
00000acb  Reset_Handler                 
00000623  SPI0_IRQHandler               
00000623  SPI1_IRQHandler               
00000623  SVC_Handler                   
0000090d  SYSCFG_DL_GPIO_init           
00000779  SYSCFG_DL_I2C_OLED_init       
00000811  SYSCFG_DL_SYSCTL_init         
00000a3f  SYSCFG_DL_init                
00000941  SYSCFG_DL_initPower           
00000a91  SysTick_Handler               
00000851  SysTick_Init                  
00000623  TIMA0_IRQHandler              
00000623  TIMA1_IRQHandler              
00000623  TIMG0_IRQHandler              
00000623  TIMG12_IRQHandler             
00000623  TIMG6_IRQHandler              
00000623  TIMG7_IRQHandler              
00000623  TIMG8_IRQHandler              
00000623  UART0_IRQHandler              
00000623  UART1_IRQHandler              
00000623  UART2_IRQHandler              
00000623  UART3_IRQHandler              
20208000  __STACK_END                   
00000200  __STACK_SIZE                  
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
0000132c  __TI_CINIT_Base               
0000133c  __TI_CINIT_Limit              
0000133c  __TI_CINIT_Warm               
00001318  __TI_Handler_Table_Base       
00001324  __TI_Handler_Table_Limit      
000008d1  __TI_auto_init_nobinit_nopinit
00000625  __TI_decompress_lzss          
00000a7d  __TI_decompress_none          
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
00000000  __TI_static_base__            
00000a29  __TI_zero_init_nomemset       
00000a53  __aeabi_idiv0                 
00000ab9  __aeabi_memcpy                
00000ab9  __aeabi_memcpy4               
00000ab9  __aeabi_memcpy8               
00000891  __aeabi_uidiv                 
00000891  __aeabi_uidivmod              
ffffffff  __binit__                     
UNDEFED   __mpu_init                    
20207e00  __stack                       
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
000009a9  _c_int00_noargs               
UNDEFED   _system_post_cinit            
00000acf  _system_pre_init              
00000ac1  abort                         
000010c8  asc2_0806                     
00000ad8  asc2_1608                     
ffffffff  binit                         
00000000  interruptVectors              
00000aa1  led_blink_task                
000009f7  main                          
00000589  memcpy                        
00000975  mspm0_delay_ms                
00000a11  mspm0_get_clock_ms            
00000a55  oled_task                     
00000a69  scheduler_init                
000007cd  scheduler_run                 
2020001c  start_time                    
20200018  sys_tick                      
20200020  task_num                      


GLOBAL SYMBOLS: SORTED BY Symbol Address 

address   name                          
-------   ----                          
00000000  __TI_ATRegion0_region_sz      
00000000  __TI_ATRegion0_src_addr       
00000000  __TI_ATRegion0_trg_addr       
00000000  __TI_ATRegion1_region_sz      
00000000  __TI_ATRegion1_src_addr       
00000000  __TI_ATRegion1_trg_addr       
00000000  __TI_ATRegion2_region_sz      
00000000  __TI_ATRegion2_src_addr       
00000000  __TI_ATRegion2_trg_addr       
00000000  __TI_static_base__            
00000000  interruptVectors              
000000c1  OLED_Init                     
00000200  __STACK_SIZE                  
00000379  OLED_WR_Byte                  
000004ad  OLED_ShowChar                 
00000589  memcpy                        
00000623  ADC0_IRQHandler               
00000623  ADC1_IRQHandler               
00000623  AES_IRQHandler                
00000623  CANFD0_IRQHandler             
00000623  DAC0_IRQHandler               
00000623  DMA_IRQHandler                
00000623  Default_Handler               
00000623  GROUP0_IRQHandler             
00000623  GROUP1_IRQHandler             
00000623  HardFault_Handler             
00000623  I2C0_IRQHandler               
00000623  I2C1_IRQHandler               
00000623  NMI_Handler                   
00000623  PendSV_Handler                
00000623  RTC_IRQHandler                
00000623  SPI0_IRQHandler               
00000623  SPI1_IRQHandler               
00000623  SVC_Handler                   
00000623  TIMA0_IRQHandler              
00000623  TIMA1_IRQHandler              
00000623  TIMG0_IRQHandler              
00000623  TIMG12_IRQHandler             
00000623  TIMG6_IRQHandler              
00000623  TIMG7_IRQHandler              
00000623  TIMG8_IRQHandler              
00000623  UART0_IRQHandler              
00000623  UART1_IRQHandler              
00000623  UART2_IRQHandler              
00000623  UART3_IRQHandler              
00000625  __TI_decompress_lzss          
000006a1  OLED_ShowString               
0000071b  DL_I2C_fillControllerTXFIFO   
00000779  SYSCFG_DL_I2C_OLED_init       
000007cd  scheduler_run                 
00000811  SYSCFG_DL_SYSCTL_init         
00000851  SysTick_Init                  
00000891  __aeabi_uidiv                 
00000891  __aeabi_uidivmod              
000008d1  __TI_auto_init_nobinit_nopinit
0000090d  SYSCFG_DL_GPIO_init           
00000941  SYSCFG_DL_initPower           
00000975  mspm0_delay_ms                
000009a9  _c_int00_noargs               
000009d1  DL_I2C_setClockConfig         
000009f7  main                          
00000a11  mspm0_get_clock_ms            
00000a29  __TI_zero_init_nomemset       
00000a3f  SYSCFG_DL_init                
00000a53  __aeabi_idiv0                 
00000a55  oled_task                     
00000a69  scheduler_init                
00000a7d  __TI_decompress_none          
00000a91  SysTick_Handler               
00000aa1  led_blink_task                
00000aad  DL_Common_delayCycles         
00000ab9  __aeabi_memcpy                
00000ab9  __aeabi_memcpy4               
00000ab9  __aeabi_memcpy8               
00000ac1  abort                         
00000ac6  C$$EXIT                       
00000ac7  HOSTexit                      
00000acb  Reset_Handler                 
00000acf  _system_pre_init              
00000ad8  asc2_1608                     
000010c8  asc2_0806                     
00001318  __TI_Handler_Table_Base       
00001324  __TI_Handler_Table_Limit      
0000132c  __TI_CINIT_Base               
0000133c  __TI_CINIT_Limit              
0000133c  __TI_CINIT_Warm               
20200000  __start___llvm_prf_bits       
20200000  __start___llvm_prf_cnts       
20200000  __stop___llvm_prf_bits        
20200000  __stop___llvm_prf_cnts        
20200018  sys_tick                      
2020001c  start_time                    
20200020  task_num                      
20207e00  __stack                       
20208000  __STACK_END                   
ffffffff  __TI_pprof_out_hndl           
ffffffff  __TI_prof_data_size           
ffffffff  __TI_prof_data_start          
ffffffff  __binit__                     
ffffffff  binit                         
UNDEFED   __mpu_init                    
UNDEFED   _system_post_cinit            

[102 symbols]
