# 修复构建脚本
Write-Host "开始修复构建问题..."

# 步骤1: 清理构建
Write-Host "清理构建..."
& "D:\TI\CCS\ccs\utils\bin\gmake.exe" -C Debug clean

# 步骤2: 修复makefile中的GEN_CMDS__FLAG
Write-Host "修复makefile..."
$makefilePath = "Debug\makefile"
if (Test-Path $makefilePath) {
    $content = Get-Content $makefilePath
    $content = $content -replace 'GEN_CMDS__FLAG := -Wl,-l"./device_linker.cmd"', 'GEN_CMDS__FLAG :='
    Set-Content -Path $makefilePath -Value $content -Encoding UTF8
}

# 步骤3: 删除Debug目录中的device_linker.cmd（如果存在）
$debugLinkerPath = "Debug\device_linker.cmd"
if (Test-Path $debugLinkerPath) {
    Remove-Item $debugLinkerPath -Force
    Write-Host "删除了Debug目录中的device_linker.cmd"
}

# 步骤4: 构建项目
Write-Host "开始构建..."
& "D:\TI\CCS\ccs\utils\bin\gmake.exe" -C Debug -k -j 8 all -O

# 步骤5: 检查构建结果
if (Test-Path "Debug\empty.out") {
    Write-Host "构建成功！" -ForegroundColor Green
} else {
    Write-Host "构建失败，尝试修复..." -ForegroundColor Yellow
    
    # 如果构建失败，再次修复makefile
    if (Test-Path $makefilePath) {
        $content = Get-Content $makefilePath
        $content = $content -replace 'GEN_CMDS__FLAG := -Wl,-l"./device_linker.cmd"', 'GEN_CMDS__FLAG :='
        Set-Content -Path $makefilePath -Value $content -Encoding UTF8
    }
    
    # 删除可能重新生成的device_linker.cmd
    if (Test-Path $debugLinkerPath) {
        Remove-Item $debugLinkerPath -Force
    }
    
    # 再次尝试构建
    Write-Host "再次尝试构建..."
    & "D:\TI\CCS\ccs\utils\bin\gmake.exe" -C Debug -k -j 8 all -O
    
    if (Test-Path "Debug\empty.out") {
        Write-Host "第二次构建成功！" -ForegroundColor Green
    } else {
        Write-Host "构建仍然失败" -ForegroundColor Red
    }
}

Write-Host "脚本执行完成"
