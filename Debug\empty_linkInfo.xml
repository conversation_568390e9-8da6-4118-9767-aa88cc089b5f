<?xml version="1.0" encoding="ISO-8859-1" ?>
<link_info>
   <banner>TI ARM Clang Linker PC v4.0.3.LTS</banner>
   <copyright>Copyright (c) 1996-2018 Texas Instruments Incorporated</copyright>
   <command_line>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\bin\tiarmlnk -ID:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib -o empty.out -mempty.map -iD:/TI/CCS/mspm0_sdk_2_05_00_05/source -iD:/Projects/TI/empty -iD:/Projects/TI/empty/Debug/syscfg -iD:/TI/CCS/ccs/tools/compiler/ti-cgt-armllvm_4.0.3.LTS/lib --diag_wrap=off --display_error_number --warn_sections --xml_link_info=empty_linkInfo.xml --rom_model ./empty.o ./ti_msp_dl_config.o ./startup_mspm0g350x_ticlang.o ./scheduler.o ./APP/oled_app.o ./BSP/OLED/oled_hardware_i2c.o ./System/clock.o ./System/interrupt.o ../device_linker.cmd -l./device_linker.cmd -ldevice.cmd.genlibs -llibc.a --start-group -llibc++.a -llibc++abi.a -llibc.a -llibsys.a -llibsysbm.a -llibclang_rt.builtins.a -llibclang_rt.profile.a --end-group --cg_opt_level=2</command_line>
   <link_time>0x685e17ed</link_time>
   <link_errors>0x4</link_errors>
   <output_file>D:\Projects\TI\empty\Debug\empty.out</output_file>
   <entry_point>
      <name>_c_int00_noargs</name>
      <address>0xb55</address>
   </entry_point>
   <input_file_list>
      <input_file id="fl-1">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>empty.o</file>
         <name>empty.o</name>
      </input_file>
      <input_file id="fl-2">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>ti_msp_dl_config.o</file>
         <name>ti_msp_dl_config.o</name>
      </input_file>
      <input_file id="fl-3">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>startup_mspm0g350x_ticlang.o</file>
         <name>startup_mspm0g350x_ticlang.o</name>
      </input_file>
      <input_file id="fl-4">
         <path>D:\Projects\TI\empty\Debug\.\</path>
         <kind>object</kind>
         <file>scheduler.o</file>
         <name>scheduler.o</name>
      </input_file>
      <input_file id="fl-5">
         <path>D:\Projects\TI\empty\Debug\.\APP\</path>
         <kind>object</kind>
         <file>oled_app.o</file>
         <name>oled_app.o</name>
      </input_file>
      <input_file id="fl-6">
         <path>D:\Projects\TI\empty\Debug\.\BSP\OLED\</path>
         <kind>object</kind>
         <file>oled_hardware_i2c.o</file>
         <name>oled_hardware_i2c.o</name>
      </input_file>
      <input_file id="fl-7">
         <path>D:\Projects\TI\empty\Debug\.\System\</path>
         <kind>object</kind>
         <file>clock.o</file>
         <name>clock.o</name>
      </input_file>
      <input_file id="fl-8">
         <path>D:\Projects\TI\empty\Debug\.\System\</path>
         <kind>object</kind>
         <file>interrupt.o</file>
         <name>interrupt.o</name>
      </input_file>
      <input_file id="fl-16">
         <path>D:\Projects\TI\empty\Debug\</path>
         <kind>object</kind>
         <file>&lt;internal&gt;</file>
         <name>&lt;internal&gt;</name>
      </input_file>
      <input_file id="fl-17">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_common.o</name>
      </input_file>
      <input_file id="fl-18">
         <path>D:\TI\CCS\mspm0_sdk_2_05_00_05\source\ti\driverlib\lib\ticlang\m0p\mspm0g1x0x_g3x0x\</path>
         <kind>archive</kind>
         <file>driverlib.a</file>
         <name>dl_i2c.o</name>
      </input_file>
      <input_file id="fl-2f">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>boot_cortex_m.c.obj</name>
      </input_file>
      <input_file id="fl-30">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>mathacl_init.c.obj</name>
      </input_file>
      <input_file id="fl-31">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>autoinit.c.obj</name>
      </input_file>
      <input_file id="fl-32">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>pre_init.c.obj</name>
      </input_file>
      <input_file id="fl-33">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>cpy_tbl.c.obj</name>
      </input_file>
      <input_file id="fl-34">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_zero_init.c.obj</name>
      </input_file>
      <input_file id="fl-35">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_none.c.obj</name>
      </input_file>
      <input_file id="fl-36">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>copy_decompress_lzss.c.obj</name>
      </input_file>
      <input_file id="fl-37">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>exit.c.obj</name>
      </input_file>
      <input_file id="fl-38">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>_lock.c.obj</name>
      </input_file>
      <input_file id="fl-39">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>args_main.c.obj</name>
      </input_file>
      <input_file id="fl-d9">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libsysbm.a</file>
         <name>hostexit.c.obj</name>
      </input_file>
      <input_file id="fl-da">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memcpy.S.obj</name>
      </input_file>
      <input_file id="fl-db">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_memset.S.obj</name>
      </input_file>
      <input_file id="fl-dc">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_uidivmod.S.obj</name>
      </input_file>
      <input_file id="fl-dd">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\</path>
         <kind>archive</kind>
         <file>libclang_rt.builtins.a</file>
         <name>aeabi_div0.c.obj</name>
      </input_file>
      <input_file id="fl-de">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memcpy16.S.obj</name>
      </input_file>
      <input_file id="fl-df">
         <path>D:\TI\CCS\ccs\tools\compiler\ti-cgt-armllvm_4.0.3.LTS\lib\armv6m-ti-none-eabi\c\</path>
         <kind>archive</kind>
         <file>libc.a</file>
         <name>memset16.S.obj</name>
      </input_file>
   </input_file_list>
   <object_component_list>
      <object_component id="oc-2a">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-aa">
         <name>.text.OLED_Init</name>
         <load_address>0xc0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc0</run_address>
         <size>0x2b8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-116">
         <name>.text.OLED_Clear</name>
         <load_address>0x378</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x378</run_address>
         <size>0x144</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-e5">
         <name>.text.OLED_WR_Byte</name>
         <load_address>0x4bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x4bc</run_address>
         <size>0x134</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-11d">
         <name>.text.OLED_ShowChar</name>
         <load_address>0x5f0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x5f0</run_address>
         <size>0xdc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-97">
         <name>.text:memcpy</name>
         <load_address>0x6cc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x6cc</run_address>
         <size>0x9a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-45">
         <name>.text.Default_Handler</name>
         <load_address>0x766</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x766</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-2e">
         <name>.text:decompress:lzss</name>
         <load_address>0x768</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x768</run_address>
         <size>0x7c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-117">
         <name>.text.OLED_ShowString</name>
         <load_address>0x7e4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x7e4</run_address>
         <size>0x7a</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-103">
         <name>.text.DL_I2C_fillControllerTXFIFO</name>
         <load_address>0x85e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x85e</run_address>
         <size>0x5e</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-d8">
         <name>.text.SYSCFG_DL_I2C_OLED_init</name>
         <load_address>0x8bc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x8bc</run_address>
         <size>0x54</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b0">
         <name>.text.scheduler_run</name>
         <load_address>0x910</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x910</run_address>
         <size>0x44</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-d6">
         <name>.text.SYSCFG_DL_SYSCTL_init</name>
         <load_address>0x954</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x954</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-11e">
         <name>.text.__aeabi_uidivmod</name>
         <load_address>0x994</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x994</run_address>
         <size>0x40</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-7d">
         <name>.text.main</name>
         <load_address>0x9d4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0x9d4</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-105">
         <name>.text.oled_task</name>
         <load_address>0xa10</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa10</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-bb">
         <name>.text:__TI_auto_init_nobinit_nopinit</name>
         <load_address>0xa4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa4c</run_address>
         <size>0x3c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-d5">
         <name>.text.SYSCFG_DL_GPIO_init</name>
         <load_address>0xa88</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xa88</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-d4">
         <name>.text.SYSCFG_DL_initPower</name>
         <load_address>0xabc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xabc</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-e4">
         <name>.text.mspm0_delay_ms</name>
         <load_address>0xaf0</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xaf0</run_address>
         <size>0x34</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-d9">
         <name>.text.SYSCFG_DL_SYSTICK_init</name>
         <load_address>0xb24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb24</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-65">
         <name>.text:_c_int00_noargs</name>
         <load_address>0xb54</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb54</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-fa">
         <name>.text.DL_I2C_setClockConfig</name>
         <load_address>0xb7c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xb7c</run_address>
         <size>0x26</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-9c">
         <name>.text.SYSCFG_DL_init</name>
         <load_address>0xba2</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xba2</run_address>
         <size>0x18</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-124">
         <name>.text.__aeabi_idiv0</name>
         <load_address>0xbba</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbba</run_address>
         <size>0x2</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-102">
         <name>.text.mspm0_get_clock_ms</name>
         <load_address>0xbbc</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbbc</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-5c">
         <name>.text:decompress:ZI:__TI_zero_init_nomemset</name>
         <load_address>0xbd4</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbd4</run_address>
         <size>0x16</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-a1">
         <name>.text.scheduler_init</name>
         <load_address>0xbec</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xbec</run_address>
         <size>0x14</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-32">
         <name>.text:decompress:none</name>
         <load_address>0xc00</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc00</run_address>
         <size>0x12</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-46">
         <name>.text.SysTick_Handler</name>
         <load_address>0xc14</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc14</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-dc">
         <name>.text.SysTick_Init</name>
         <load_address>0xc24</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc24</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-104">
         <name>.text.led_blink_task</name>
         <load_address>0xc34</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc34</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a6">
         <name>.text.DL_Common_delayCycles</name>
         <load_address>0xc40</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc40</run_address>
         <size>0xa</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-55">
         <name>.text.__aeabi_memcpy</name>
         <load_address>0xc4c</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc4c</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-b4">
         <name>.text:abort</name>
         <load_address>0xc54</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc54</run_address>
         <size>0x6</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-e9">
         <name>.text.HOSTexit</name>
         <load_address>0xc5a</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc5a</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-41">
         <name>.text.Reset_Handler</name>
         <load_address>0xc5e</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc5e</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-79">
         <name>.text._system_pre_init</name>
         <load_address>0xc62</load_address>
         <readonly>true</readonly>
         <executable>true</executable>
         <run_address>0xc62</run_address>
         <size>0x4</size>
         <alignment>0x2</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-180">
         <name>.cinit..data.load</name>
         <load_address>0x1498</load_address>
         <readonly>true</readonly>
         <run_address>0x1498</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-17e">
         <name>__TI_handler_table</name>
         <load_address>0x14ac</load_address>
         <readonly>true</readonly>
         <run_address>0x14ac</run_address>
         <size>0xc</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-181">
         <name>.cinit..bss.load</name>
         <load_address>0x14b8</load_address>
         <readonly>true</readonly>
         <run_address>0x14b8</run_address>
         <size>0x8</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-17f">
         <name>__TI_cinit_table</name>
         <load_address>0x14c0</load_address>
         <readonly>true</readonly>
         <run_address>0x14c0</run_address>
         <size>0x10</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-122">
         <name>.rodata.asc2_1608</name>
         <load_address>0xc68</load_address>
         <readonly>true</readonly>
         <run_address>0xc68</run_address>
         <size>0x5f0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-123">
         <name>.rodata.asc2_0806</name>
         <load_address>0x1258</load_address>
         <readonly>true</readonly>
         <run_address>0x1258</run_address>
         <size>0x228</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-119">
         <name>.rodata.str1.17288060079586621852.1</name>
         <load_address>0x1480</load_address>
         <readonly>true</readonly>
         <run_address>0x1480</run_address>
         <size>0xc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-11a">
         <name>.rodata.str1.865768355259620247.1</name>
         <load_address>0x148c</load_address>
         <readonly>true</readonly>
         <run_address>0x148c</run_address>
         <size>0x9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-100">
         <name>.rodata.gI2C_OLEDClockConfig</name>
         <load_address>0x1495</load_address>
         <readonly>true</readonly>
         <run_address>0x1495</run_address>
         <size>0x2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-136">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <readonly>true</readonly>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-e8">
         <name>.data.scheduler_task</name>
         <load_address>0x20200000</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200000</run_address>
         <size>0x18</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-118">
         <name>.data.oled_init_done</name>
         <load_address>0x2020001c</load_address>
         <readwrite>true</readwrite>
         <run_address>0x2020001c</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-6c">
         <name>.data.sys_tick</name>
         <load_address>0x20200018</load_address>
         <readwrite>true</readwrite>
         <run_address>0x20200018</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-de">
         <name>.common:task_num</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200024</run_address>
         <size>0x1</size>
         <alignment>0x1</alignment>
      </object_component>
      <object_component id="oc-101">
         <name>.common:start_time</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20200020</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
      </object_component>
      <object_component id="oc-2c">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x4</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-183">
         <name>.stack</name>
         <uninitialized>true</uninitialized>
         <readwrite>true</readwrite>
         <run_address>0x20207e00</run_address>
         <size>0x0</size>
         <alignment>0x8</alignment>
      </object_component>
      <object_component id="oc-7e">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x36</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-d7">
         <name>.debug_loc</name>
         <load_address>0x36</load_address>
         <run_address>0x36</run_address>
         <size>0x92</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-b1">
         <name>.debug_loc</name>
         <load_address>0xc8</load_address>
         <run_address>0xc8</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ad">
         <name>.debug_loc</name>
         <load_address>0x109</load_address>
         <run_address>0x109</run_address>
         <size>0x118b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-91">
         <name>.debug_loc</name>
         <load_address>0x1294</load_address>
         <run_address>0x1294</run_address>
         <size>0x83</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-e3">
         <name>.debug_loc</name>
         <load_address>0x1317</load_address>
         <run_address>0x1317</run_address>
         <size>0x13</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-fc">
         <name>.debug_loc</name>
         <load_address>0x132a</load_address>
         <run_address>0x132a</run_address>
         <size>0x352</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-50">
         <name>.debug_loc</name>
         <load_address>0x167c</load_address>
         <run_address>0x167c</run_address>
         <size>0xd8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-c0">
         <name>.debug_loc</name>
         <load_address>0x1754</load_address>
         <run_address>0x1754</run_address>
         <size>0x424</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-60">
         <name>.debug_loc</name>
         <load_address>0x1b78</load_address>
         <run_address>0x1b78</run_address>
         <size>0x16c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-5b">
         <name>.debug_loc</name>
         <load_address>0x1ce4</load_address>
         <run_address>0x1ce4</run_address>
         <size>0x6f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-53">
         <name>.debug_loc</name>
         <load_address>0x1d53</load_address>
         <run_address>0x1d53</run_address>
         <size>0x167</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-ba">
         <name>.debug_loc</name>
         <load_address>0x1eba</load_address>
         <run_address>0x1eba</run_address>
         <size>0x26</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-12c">
         <name>.debug_loc</name>
         <load_address>0x1ee0</load_address>
         <run_address>0x1ee0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-b2">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x11c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-da">
         <name>.debug_abbrev</name>
         <load_address>0x11c</load_address>
         <run_address>0x11c</run_address>
         <size>0x1f5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4a">
         <name>.debug_abbrev</name>
         <load_address>0x311</load_address>
         <run_address>0x311</run_address>
         <size>0x6d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-df">
         <name>.debug_abbrev</name>
         <load_address>0x37e</load_address>
         <run_address>0x37e</run_address>
         <size>0xdc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-11b">
         <name>.debug_abbrev</name>
         <load_address>0x45a</load_address>
         <run_address>0x45a</run_address>
         <size>0x90</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-e6">
         <name>.debug_abbrev</name>
         <load_address>0x4ea</load_address>
         <run_address>0x4ea</run_address>
         <size>0x2a4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-8d">
         <name>.debug_abbrev</name>
         <load_address>0x78e</load_address>
         <run_address>0x78e</run_address>
         <size>0xe4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-6e">
         <name>.debug_abbrev</name>
         <load_address>0x872</load_address>
         <run_address>0x872</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-e1">
         <name>.debug_abbrev</name>
         <load_address>0x89e</load_address>
         <run_address>0x89e</run_address>
         <size>0x62</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-114">
         <name>.debug_abbrev</name>
         <load_address>0x900</load_address>
         <run_address>0x900</run_address>
         <size>0x1e7</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-4c">
         <name>.debug_abbrev</name>
         <load_address>0xae7</load_address>
         <run_address>0xae7</run_address>
         <size>0xaf</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-f3">
         <name>.debug_abbrev</name>
         <load_address>0xb96</load_address>
         <run_address>0xb96</run_address>
         <size>0x170</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-9a">
         <name>.debug_abbrev</name>
         <load_address>0xd06</load_address>
         <run_address>0xd06</run_address>
         <size>0x39</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-72">
         <name>.debug_abbrev</name>
         <load_address>0xd3f</load_address>
         <run_address>0xd3f</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-59">
         <name>.debug_abbrev</name>
         <load_address>0xe01</load_address>
         <run_address>0xe01</run_address>
         <size>0x70</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-51">
         <name>.debug_abbrev</name>
         <load_address>0xe71</load_address>
         <run_address>0xe71</run_address>
         <size>0x8d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-ed">
         <name>.debug_abbrev</name>
         <load_address>0xefe</load_address>
         <run_address>0xefe</run_address>
         <size>0x98</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-109">
         <name>.debug_abbrev</name>
         <load_address>0xf96</load_address>
         <run_address>0xf96</run_address>
         <size>0x2c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-71">
         <name>.debug_abbrev</name>
         <load_address>0xfc2</load_address>
         <run_address>0xfc2</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-129">
         <name>.debug_abbrev</name>
         <load_address>0xfe9</load_address>
         <run_address>0xfe9</run_address>
         <size>0x27</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-12a">
         <name>.debug_abbrev</name>
         <load_address>0x1010</load_address>
         <run_address>0x1010</run_address>
         <size>0x59</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-cd">
         <name>.debug_abbrev</name>
         <load_address>0x1069</load_address>
         <run_address>0x1069</run_address>
         <size>0x25</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-185">
         <name>.debug_abbrev</name>
         <load_address>0x108e</load_address>
         <run_address>0x108e</run_address>
         <size>0xf</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-7f">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x78b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9d">
         <name>.debug_info</name>
         <load_address>0x78b</load_address>
         <run_address>0x78b</run_address>
         <size>0x1fb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-2b">
         <name>.debug_info</name>
         <load_address>0x2744</load_address>
         <run_address>0x2744</run_address>
         <size>0x80</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a2">
         <name>.debug_info</name>
         <load_address>0x27c4</load_address>
         <run_address>0x27c4</run_address>
         <size>0x13f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-108">
         <name>.debug_info</name>
         <load_address>0x2903</load_address>
         <run_address>0x2903</run_address>
         <size>0xd3</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-ab">
         <name>.debug_info</name>
         <load_address>0x29d6</load_address>
         <run_address>0x29d6</run_address>
         <size>0x2616</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-6d">
         <name>.debug_info</name>
         <load_address>0x4fec</load_address>
         <run_address>0x4fec</run_address>
         <size>0x147</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-49">
         <name>.debug_info</name>
         <load_address>0x5133</load_address>
         <run_address>0x5133</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-a7">
         <name>.debug_info</name>
         <load_address>0x516e</load_address>
         <run_address>0x516e</run_address>
         <size>0x75</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-fd">
         <name>.debug_info</name>
         <load_address>0x51e3</load_address>
         <run_address>0x51e3</run_address>
         <size>0xcc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-2d">
         <name>.debug_info</name>
         <load_address>0x5ea5</load_address>
         <run_address>0x5ea5</run_address>
         <size>0x423</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-bc">
         <name>.debug_info</name>
         <load_address>0x62c8</load_address>
         <run_address>0x62c8</run_address>
         <size>0x744</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-7a">
         <name>.debug_info</name>
         <load_address>0x6a0c</load_address>
         <run_address>0x6a0c</run_address>
         <size>0x46</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-5f">
         <name>.debug_info</name>
         <load_address>0x6a52</load_address>
         <run_address>0x6a52</run_address>
         <size>0x192</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-33">
         <name>.debug_info</name>
         <load_address>0x6be4</load_address>
         <run_address>0x6be4</run_address>
         <size>0xc6</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-30">
         <name>.debug_info</name>
         <load_address>0x6caa</load_address>
         <run_address>0x6caa</run_address>
         <size>0x17c</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-b5">
         <name>.debug_info</name>
         <load_address>0x6e26</load_address>
         <run_address>0x6e26</run_address>
         <size>0xf8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-ea">
         <name>.debug_info</name>
         <load_address>0x6f1e</load_address>
         <run_address>0x6f1e</run_address>
         <size>0x3b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-56">
         <name>.debug_info</name>
         <load_address>0x6f59</load_address>
         <run_address>0x6f59</run_address>
         <size>0x199</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-11f">
         <name>.debug_info</name>
         <load_address>0x70f2</load_address>
         <run_address>0x70f2</run_address>
         <size>0x1bc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-125">
         <name>.debug_info</name>
         <load_address>0x72ae</load_address>
         <run_address>0x72ae</run_address>
         <size>0x85</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-93">
         <name>.debug_info</name>
         <load_address>0x7333</load_address>
         <run_address>0x7333</run_address>
         <size>0x2fa</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-184">
         <name>.debug_info</name>
         <load_address>0x762d</load_address>
         <run_address>0x762d</run_address>
         <size>0x8e</size>
         <alignment>0x0</alignment>
      </object_component>
      <object_component id="oc-80">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9e">
         <name>.debug_ranges</name>
         <load_address>0x18</load_address>
         <run_address>0x18</run_address>
         <size>0x38</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-44">
         <name>.debug_ranges</name>
         <load_address>0x50</load_address>
         <run_address>0x50</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a3">
         <name>.debug_ranges</name>
         <load_address>0x68</load_address>
         <run_address>0x68</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-ac">
         <name>.debug_ranges</name>
         <load_address>0x80</load_address>
         <run_address>0x80</run_address>
         <size>0x1b0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-90">
         <name>.debug_ranges</name>
         <load_address>0x230</load_address>
         <run_address>0x230</run_address>
         <size>0x30</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-fb">
         <name>.debug_ranges</name>
         <load_address>0x260</load_address>
         <run_address>0x260</run_address>
         <size>0x1d8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-4f">
         <name>.debug_ranges</name>
         <load_address>0x438</load_address>
         <run_address>0x438</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-be">
         <name>.debug_ranges</name>
         <load_address>0x480</load_address>
         <run_address>0x480</run_address>
         <size>0x48</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-5e">
         <name>.debug_ranges</name>
         <load_address>0x4c8</load_address>
         <run_address>0x4c8</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-54">
         <name>.debug_ranges</name>
         <load_address>0x4e0</load_address>
         <run_address>0x4e0</run_address>
         <size>0x50</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-b6">
         <name>.debug_ranges</name>
         <load_address>0x530</load_address>
         <run_address>0x530</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-126">
         <name>.debug_ranges</name>
         <load_address>0x548</load_address>
         <run_address>0x548</run_address>
         <size>0x18</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-96">
         <name>.debug_ranges</name>
         <load_address>0x560</load_address>
         <run_address>0x560</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-b3">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4a0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-db">
         <name>.debug_str</name>
         <load_address>0x4a0</load_address>
         <run_address>0x4a0</run_address>
         <size>0x1a2a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-4b">
         <name>.debug_str</name>
         <load_address>0x1eca</load_address>
         <run_address>0x1eca</run_address>
         <size>0x149</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-e0">
         <name>.debug_str</name>
         <load_address>0x2013</load_address>
         <run_address>0x2013</run_address>
         <size>0x166</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-11c">
         <name>.debug_str</name>
         <load_address>0x2179</load_address>
         <run_address>0x2179</run_address>
         <size>0x112</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-e7">
         <name>.debug_str</name>
         <load_address>0x228b</load_address>
         <run_address>0x228b</run_address>
         <size>0xf60</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-8e">
         <name>.debug_str</name>
         <load_address>0x31eb</load_address>
         <run_address>0x31eb</run_address>
         <size>0x162</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-6f">
         <name>.debug_str</name>
         <load_address>0x334d</load_address>
         <run_address>0x334d</run_address>
         <size>0xb9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-e2">
         <name>.debug_str</name>
         <load_address>0x3406</load_address>
         <run_address>0x3406</run_address>
         <size>0x16d</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-115">
         <name>.debug_str</name>
         <load_address>0x3573</load_address>
         <run_address>0x3573</run_address>
         <size>0x8af</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-4d">
         <name>.debug_str</name>
         <load_address>0x3e22</load_address>
         <run_address>0x3e22</run_address>
         <size>0x225</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-f4">
         <name>.debug_str</name>
         <load_address>0x4047</load_address>
         <run_address>0x4047</run_address>
         <size>0x32f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-9b">
         <name>.debug_str</name>
         <load_address>0x4376</load_address>
         <run_address>0x4376</run_address>
         <size>0xf5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-73">
         <name>.debug_str</name>
         <load_address>0x446b</load_address>
         <run_address>0x446b</run_address>
         <size>0x19b</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-5a">
         <name>.debug_str</name>
         <load_address>0x4606</load_address>
         <run_address>0x4606</run_address>
         <size>0x168</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-52">
         <name>.debug_str</name>
         <load_address>0x476e</load_address>
         <run_address>0x476e</run_address>
         <size>0x1d5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-ee">
         <name>.debug_str</name>
         <load_address>0x4943</load_address>
         <run_address>0x4943</run_address>
         <size>0x148</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-10a">
         <name>.debug_str</name>
         <load_address>0x4a8b</load_address>
         <run_address>0x4a8b</run_address>
         <size>0xe9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-12b">
         <name>.debug_str</name>
         <load_address>0x4b74</load_address>
         <run_address>0x4b74</run_address>
         <size>0x193</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-82">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-9f">
         <name>.debug_frame</name>
         <load_address>0x30</load_address>
         <run_address>0x30</run_address>
         <size>0x88</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-43">
         <name>.debug_frame</name>
         <load_address>0xb8</load_address>
         <run_address>0xb8</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a4">
         <name>.debug_frame</name>
         <load_address>0xe8</load_address>
         <run_address>0xe8</run_address>
         <size>0x48</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-107">
         <name>.debug_frame</name>
         <load_address>0x130</load_address>
         <run_address>0x130</run_address>
         <size>0x2c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-ae">
         <name>.debug_frame</name>
         <load_address>0x15c</load_address>
         <run_address>0x15c</run_address>
         <size>0x1ec</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-dd">
         <name>.debug_frame</name>
         <load_address>0x348</load_address>
         <run_address>0x348</run_address>
         <size>0x68</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-47">
         <name>.debug_frame</name>
         <load_address>0x3b0</load_address>
         <run_address>0x3b0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-a8">
         <name>.debug_frame</name>
         <load_address>0x3d0</load_address>
         <run_address>0x3d0</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-ff">
         <name>.debug_frame</name>
         <load_address>0x3f0</load_address>
         <run_address>0x3f0</run_address>
         <size>0x12c</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-64">
         <name>.debug_frame</name>
         <load_address>0x51c</load_address>
         <run_address>0x51c</run_address>
         <size>0x90</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-bf">
         <name>.debug_frame</name>
         <load_address>0x5ac</load_address>
         <run_address>0x5ac</run_address>
         <size>0x100</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-7b">
         <name>.debug_frame</name>
         <load_address>0x6ac</load_address>
         <run_address>0x6ac</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-5d">
         <name>.debug_frame</name>
         <load_address>0x6cc</load_address>
         <run_address>0x6cc</run_address>
         <size>0x38</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-34">
         <name>.debug_frame</name>
         <load_address>0x704</load_address>
         <run_address>0x704</run_address>
         <size>0x28</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-31">
         <name>.debug_frame</name>
         <load_address>0x72c</load_address>
         <run_address>0x72c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-b7">
         <name>.debug_frame</name>
         <load_address>0x75c</load_address>
         <run_address>0x75c</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-ec">
         <name>.debug_frame</name>
         <load_address>0x78c</load_address>
         <run_address>0x78c</run_address>
         <size>0x20</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-127">
         <name>.debug_frame</name>
         <load_address>0x7ac</load_address>
         <run_address>0x7ac</run_address>
         <size>0x30</size>
         <alignment>0x4</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-81">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x207</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-1"/>
      </object_component>
      <object_component id="oc-a0">
         <name>.debug_line</name>
         <load_address>0x207</load_address>
         <run_address>0x207</run_address>
         <size>0x4e9</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2"/>
      </object_component>
      <object_component id="oc-42">
         <name>.debug_line</name>
         <load_address>0x6f0</load_address>
         <run_address>0x6f0</run_address>
         <size>0xbc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-3"/>
      </object_component>
      <object_component id="oc-a5">
         <name>.debug_line</name>
         <load_address>0x7ac</load_address>
         <run_address>0x7ac</run_address>
         <size>0x147</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-4"/>
      </object_component>
      <object_component id="oc-106">
         <name>.debug_line</name>
         <load_address>0x8f3</load_address>
         <run_address>0x8f3</run_address>
         <size>0x106</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-5"/>
      </object_component>
      <object_component id="oc-af">
         <name>.debug_line</name>
         <load_address>0x9f9</load_address>
         <run_address>0x9f9</run_address>
         <size>0x1054</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-6"/>
      </object_component>
      <object_component id="oc-8f">
         <name>.debug_line</name>
         <load_address>0x1a4d</load_address>
         <run_address>0x1a4d</run_address>
         <size>0x218</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-7"/>
      </object_component>
      <object_component id="oc-48">
         <name>.debug_line</name>
         <load_address>0x1c65</load_address>
         <run_address>0x1c65</run_address>
         <size>0x4f</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-8"/>
      </object_component>
      <object_component id="oc-a9">
         <name>.debug_line</name>
         <load_address>0x1cb4</load_address>
         <run_address>0x1cb4</run_address>
         <size>0x178</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-17"/>
      </object_component>
      <object_component id="oc-fe">
         <name>.debug_line</name>
         <load_address>0x1e2c</load_address>
         <run_address>0x1e2c</run_address>
         <size>0x682</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-18"/>
      </object_component>
      <object_component id="oc-4e">
         <name>.debug_line</name>
         <load_address>0x24ae</load_address>
         <run_address>0x24ae</run_address>
         <size>0x1dc</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-2f"/>
      </object_component>
      <object_component id="oc-bd">
         <name>.debug_line</name>
         <load_address>0x268a</load_address>
         <run_address>0x268a</run_address>
         <size>0x51a</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-31"/>
      </object_component>
      <object_component id="oc-7c">
         <name>.debug_line</name>
         <load_address>0x2ba4</load_address>
         <run_address>0x2ba4</run_address>
         <size>0x3e</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-32"/>
      </object_component>
      <object_component id="oc-61">
         <name>.debug_line</name>
         <load_address>0x2be2</load_address>
         <run_address>0x2be2</run_address>
         <size>0xfe</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-34"/>
      </object_component>
      <object_component id="oc-35">
         <name>.debug_line</name>
         <load_address>0x2ce0</load_address>
         <run_address>0x2ce0</run_address>
         <size>0xc0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-35"/>
      </object_component>
      <object_component id="oc-2f">
         <name>.debug_line</name>
         <load_address>0x2da0</load_address>
         <run_address>0x2da0</run_address>
         <size>0x1c8</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-36"/>
      </object_component>
      <object_component id="oc-b8">
         <name>.debug_line</name>
         <load_address>0x2f68</load_address>
         <run_address>0x2f68</run_address>
         <size>0x67</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-37"/>
      </object_component>
      <object_component id="oc-eb">
         <name>.debug_line</name>
         <load_address>0x2fcf</load_address>
         <run_address>0x2fcf</run_address>
         <size>0x41</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-d9"/>
      </object_component>
      <object_component id="oc-58">
         <name>.debug_line</name>
         <load_address>0x3010</load_address>
         <run_address>0x3010</run_address>
         <size>0xa4</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-121">
         <name>.debug_line</name>
         <load_address>0x30b4</load_address>
         <run_address>0x30b4</run_address>
         <size>0xc2</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-128">
         <name>.debug_line</name>
         <load_address>0x3176</load_address>
         <run_address>0x3176</run_address>
         <size>0xb5</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dd"/>
      </object_component>
      <object_component id="oc-95">
         <name>.debug_line</name>
         <load_address>0x322b</load_address>
         <run_address>0x322b</run_address>
         <size>0xa0</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
      <object_component id="oc-57">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-da"/>
      </object_component>
      <object_component id="oc-120">
         <name>.debug_aranges</name>
         <load_address>0x20</load_address>
         <run_address>0x20</run_address>
         <size>0x20</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-dc"/>
      </object_component>
      <object_component id="oc-94">
         <name>.debug_aranges</name>
         <load_address>0x40</load_address>
         <run_address>0x40</run_address>
         <size>0x28</size>
         <alignment>0x1</alignment>
         <input_file_ref idref="fl-de"/>
      </object_component>
   </object_component_list>
   <logical_group_list>
      <logical_group id="lg-2" display="no" color="cyan">
         <name>.intvecs</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0xc0</size>
         <contents>
            <object_component_ref idref="oc-2a"/>
         </contents>
      </logical_group>
      <logical_group id="lg-3" display="no" color="cyan">
         <name>.text</name>
         <load_address>0xc0</load_address>
         <run_address>0xc0</run_address>
         <size>0xba8</size>
         <contents>
            <object_component_ref idref="oc-aa"/>
            <object_component_ref idref="oc-116"/>
            <object_component_ref idref="oc-e5"/>
            <object_component_ref idref="oc-11d"/>
            <object_component_ref idref="oc-97"/>
            <object_component_ref idref="oc-45"/>
            <object_component_ref idref="oc-2e"/>
            <object_component_ref idref="oc-117"/>
            <object_component_ref idref="oc-103"/>
            <object_component_ref idref="oc-d8"/>
            <object_component_ref idref="oc-b0"/>
            <object_component_ref idref="oc-d6"/>
            <object_component_ref idref="oc-11e"/>
            <object_component_ref idref="oc-7d"/>
            <object_component_ref idref="oc-105"/>
            <object_component_ref idref="oc-bb"/>
            <object_component_ref idref="oc-d5"/>
            <object_component_ref idref="oc-d4"/>
            <object_component_ref idref="oc-e4"/>
            <object_component_ref idref="oc-d9"/>
            <object_component_ref idref="oc-65"/>
            <object_component_ref idref="oc-fa"/>
            <object_component_ref idref="oc-9c"/>
            <object_component_ref idref="oc-124"/>
            <object_component_ref idref="oc-102"/>
            <object_component_ref idref="oc-5c"/>
            <object_component_ref idref="oc-a1"/>
            <object_component_ref idref="oc-32"/>
            <object_component_ref idref="oc-46"/>
            <object_component_ref idref="oc-dc"/>
            <object_component_ref idref="oc-104"/>
            <object_component_ref idref="oc-a6"/>
            <object_component_ref idref="oc-55"/>
            <object_component_ref idref="oc-b4"/>
            <object_component_ref idref="oc-e9"/>
            <object_component_ref idref="oc-41"/>
            <object_component_ref idref="oc-79"/>
         </contents>
      </logical_group>
      <logical_group id="lg-4" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-5" display="no" color="cyan">
         <name>.cinit</name>
         <load_address>0x1498</load_address>
         <run_address>0x1498</run_address>
         <size>0x38</size>
         <contents>
            <object_component_ref idref="oc-180"/>
            <object_component_ref idref="oc-17e"/>
            <object_component_ref idref="oc-181"/>
            <object_component_ref idref="oc-17f"/>
         </contents>
      </logical_group>
      <logical_group id="lg-6" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-7" display="no" color="cyan">
         <name>.rodata</name>
         <load_address>0xc68</load_address>
         <run_address>0xc68</run_address>
         <size>0x830</size>
         <contents>
            <object_component_ref idref="oc-122"/>
            <object_component_ref idref="oc-123"/>
            <object_component_ref idref="oc-119"/>
            <object_component_ref idref="oc-11a"/>
            <object_component_ref idref="oc-100"/>
         </contents>
      </logical_group>
      <logical_group id="lg-8" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-9" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-a" display="no" color="cyan">
         <name>.binit</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
            <object_component_ref idref="oc-136"/>
         </contents>
      </logical_group>
      <logical_group id="lg-b" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-c" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-d" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-e" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x20200000</run_address>
         <size>0x1d</size>
         <contents>
            <object_component_ref idref="oc-e8"/>
            <object_component_ref idref="oc-118"/>
            <object_component_ref idref="oc-6c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-f" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x20200020</run_address>
         <size>0x5</size>
         <contents>
            <object_component_ref idref="oc-de"/>
            <object_component_ref idref="oc-101"/>
         </contents>
      </logical_group>
      <logical_group id="lg-10" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-11" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <contents>
            <object_component_ref idref="oc-2c"/>
            <object_component_ref idref="oc-183"/>
         </contents>
      </logical_group>
      <logical_group id="lg-12" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-13" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-14" display="no" color="cyan">
         <name>.intvecs</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-15" display="no" color="cyan">
         <name>.text</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-16" display="no" color="cyan">
         <name>.const</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-17" display="no" color="cyan">
         <name>.cinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-18" display="no" color="cyan">
         <name>.pinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-19" display="no" color="cyan">
         <name>.rodata</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1a" display="no" color="cyan">
         <name>.ARM.exidx</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1b" display="no" color="cyan">
         <name>.init_array</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1c" display="no" color="cyan">
         <name>.binit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1d" display="no" color="cyan">
         <name>.TI.ramfunc</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1e" display="no" color="cyan">
         <name>.vtable</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-1f" display="no" color="cyan">
         <name>.args</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-20" display="no" color="cyan">
         <name>.data</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-21" display="no" color="cyan">
         <name>.bss</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-22" display="no" color="cyan">
         <name>.sysmem</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-23" display="no" color="cyan">
         <name>.stack</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-24" display="no" color="cyan">
         <name>.BCRConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-25" display="no" color="cyan">
         <name>.BSLConfig</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-12d" display="no" color="cyan">
         <name>.TI.noinit</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-12e" display="no" color="cyan">
         <name>.TI.persistent</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-12f" display="no" color="cyan">
         <name>.TI.local</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-130" display="no" color="cyan">
         <name>.TI.onchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-131" display="no" color="cyan">
         <name>.TI.offchip</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-132" display="no" color="cyan">
         <name>__llvm_prf_cnts</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-134" display="no" color="cyan">
         <name>__llvm_prf_bits</name>
         <run_address>0x20200000</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <logical_group id="lg-162" display="never" color="cyan">
         <name>.debug_loc</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x1f00</size>
         <contents>
            <object_component_ref idref="oc-7e"/>
            <object_component_ref idref="oc-d7"/>
            <object_component_ref idref="oc-b1"/>
            <object_component_ref idref="oc-ad"/>
            <object_component_ref idref="oc-91"/>
            <object_component_ref idref="oc-e3"/>
            <object_component_ref idref="oc-fc"/>
            <object_component_ref idref="oc-50"/>
            <object_component_ref idref="oc-c0"/>
            <object_component_ref idref="oc-60"/>
            <object_component_ref idref="oc-5b"/>
            <object_component_ref idref="oc-53"/>
            <object_component_ref idref="oc-ba"/>
            <object_component_ref idref="oc-12c"/>
         </contents>
      </logical_group>
      <logical_group id="lg-164" display="never" color="cyan">
         <name>.debug_abbrev</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x109d</size>
         <contents>
            <object_component_ref idref="oc-b2"/>
            <object_component_ref idref="oc-da"/>
            <object_component_ref idref="oc-4a"/>
            <object_component_ref idref="oc-df"/>
            <object_component_ref idref="oc-11b"/>
            <object_component_ref idref="oc-e6"/>
            <object_component_ref idref="oc-8d"/>
            <object_component_ref idref="oc-6e"/>
            <object_component_ref idref="oc-e1"/>
            <object_component_ref idref="oc-114"/>
            <object_component_ref idref="oc-4c"/>
            <object_component_ref idref="oc-f3"/>
            <object_component_ref idref="oc-9a"/>
            <object_component_ref idref="oc-72"/>
            <object_component_ref idref="oc-59"/>
            <object_component_ref idref="oc-51"/>
            <object_component_ref idref="oc-ed"/>
            <object_component_ref idref="oc-109"/>
            <object_component_ref idref="oc-71"/>
            <object_component_ref idref="oc-129"/>
            <object_component_ref idref="oc-12a"/>
            <object_component_ref idref="oc-cd"/>
            <object_component_ref idref="oc-185"/>
         </contents>
      </logical_group>
      <logical_group id="lg-166" display="never" color="cyan">
         <name>.debug_info</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x76bb</size>
         <contents>
            <object_component_ref idref="oc-7f"/>
            <object_component_ref idref="oc-9d"/>
            <object_component_ref idref="oc-2b"/>
            <object_component_ref idref="oc-a2"/>
            <object_component_ref idref="oc-108"/>
            <object_component_ref idref="oc-ab"/>
            <object_component_ref idref="oc-6d"/>
            <object_component_ref idref="oc-49"/>
            <object_component_ref idref="oc-a7"/>
            <object_component_ref idref="oc-fd"/>
            <object_component_ref idref="oc-2d"/>
            <object_component_ref idref="oc-bc"/>
            <object_component_ref idref="oc-7a"/>
            <object_component_ref idref="oc-5f"/>
            <object_component_ref idref="oc-33"/>
            <object_component_ref idref="oc-30"/>
            <object_component_ref idref="oc-b5"/>
            <object_component_ref idref="oc-ea"/>
            <object_component_ref idref="oc-56"/>
            <object_component_ref idref="oc-11f"/>
            <object_component_ref idref="oc-125"/>
            <object_component_ref idref="oc-93"/>
            <object_component_ref idref="oc-184"/>
         </contents>
      </logical_group>
      <logical_group id="lg-168" display="never" color="cyan">
         <name>.debug_ranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x588</size>
         <contents>
            <object_component_ref idref="oc-80"/>
            <object_component_ref idref="oc-9e"/>
            <object_component_ref idref="oc-44"/>
            <object_component_ref idref="oc-a3"/>
            <object_component_ref idref="oc-ac"/>
            <object_component_ref idref="oc-90"/>
            <object_component_ref idref="oc-fb"/>
            <object_component_ref idref="oc-4f"/>
            <object_component_ref idref="oc-be"/>
            <object_component_ref idref="oc-5e"/>
            <object_component_ref idref="oc-54"/>
            <object_component_ref idref="oc-b6"/>
            <object_component_ref idref="oc-126"/>
            <object_component_ref idref="oc-96"/>
         </contents>
      </logical_group>
      <logical_group id="lg-16a" display="never" color="cyan">
         <name>.debug_str</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x4d07</size>
         <contents>
            <object_component_ref idref="oc-b3"/>
            <object_component_ref idref="oc-db"/>
            <object_component_ref idref="oc-4b"/>
            <object_component_ref idref="oc-e0"/>
            <object_component_ref idref="oc-11c"/>
            <object_component_ref idref="oc-e7"/>
            <object_component_ref idref="oc-8e"/>
            <object_component_ref idref="oc-6f"/>
            <object_component_ref idref="oc-e2"/>
            <object_component_ref idref="oc-115"/>
            <object_component_ref idref="oc-4d"/>
            <object_component_ref idref="oc-f4"/>
            <object_component_ref idref="oc-9b"/>
            <object_component_ref idref="oc-73"/>
            <object_component_ref idref="oc-5a"/>
            <object_component_ref idref="oc-52"/>
            <object_component_ref idref="oc-ee"/>
            <object_component_ref idref="oc-10a"/>
            <object_component_ref idref="oc-12b"/>
         </contents>
      </logical_group>
      <logical_group id="lg-16c" display="never" color="cyan">
         <name>.debug_frame</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x7dc</size>
         <contents>
            <object_component_ref idref="oc-82"/>
            <object_component_ref idref="oc-9f"/>
            <object_component_ref idref="oc-43"/>
            <object_component_ref idref="oc-a4"/>
            <object_component_ref idref="oc-107"/>
            <object_component_ref idref="oc-ae"/>
            <object_component_ref idref="oc-dd"/>
            <object_component_ref idref="oc-47"/>
            <object_component_ref idref="oc-a8"/>
            <object_component_ref idref="oc-ff"/>
            <object_component_ref idref="oc-64"/>
            <object_component_ref idref="oc-bf"/>
            <object_component_ref idref="oc-7b"/>
            <object_component_ref idref="oc-5d"/>
            <object_component_ref idref="oc-34"/>
            <object_component_ref idref="oc-31"/>
            <object_component_ref idref="oc-b7"/>
            <object_component_ref idref="oc-ec"/>
            <object_component_ref idref="oc-127"/>
         </contents>
      </logical_group>
      <logical_group id="lg-16e" display="never" color="cyan">
         <name>.debug_line</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x32cb</size>
         <contents>
            <object_component_ref idref="oc-81"/>
            <object_component_ref idref="oc-a0"/>
            <object_component_ref idref="oc-42"/>
            <object_component_ref idref="oc-a5"/>
            <object_component_ref idref="oc-106"/>
            <object_component_ref idref="oc-af"/>
            <object_component_ref idref="oc-8f"/>
            <object_component_ref idref="oc-48"/>
            <object_component_ref idref="oc-a9"/>
            <object_component_ref idref="oc-fe"/>
            <object_component_ref idref="oc-4e"/>
            <object_component_ref idref="oc-bd"/>
            <object_component_ref idref="oc-7c"/>
            <object_component_ref idref="oc-61"/>
            <object_component_ref idref="oc-35"/>
            <object_component_ref idref="oc-2f"/>
            <object_component_ref idref="oc-b8"/>
            <object_component_ref idref="oc-eb"/>
            <object_component_ref idref="oc-58"/>
            <object_component_ref idref="oc-121"/>
            <object_component_ref idref="oc-128"/>
            <object_component_ref idref="oc-95"/>
         </contents>
      </logical_group>
      <logical_group id="lg-178" display="never" color="cyan">
         <name>.debug_aranges</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x68</size>
         <contents>
            <object_component_ref idref="oc-57"/>
            <object_component_ref idref="oc-120"/>
            <object_component_ref idref="oc-94"/>
         </contents>
      </logical_group>
      <logical_group id="lg-182" display="no" color="cyan">
         <name>Veneer$$CMSE</name>
         <run_address>0x0</run_address>
         <size>0x0</size>
         <contents>
         </contents>
      </logical_group>
      <load_segment id="lg-18c" display="no" color="cyan">
         <name>SEGMENT_0</name>
         <load_address>0x0</load_address>
         <run_address>0x0</run_address>
         <size>0x14d0</size>
         <flags>0x5</flags>
         <contents>
            <logical_group_ref idref="lg-2"/>
            <logical_group_ref idref="lg-3"/>
            <logical_group_ref idref="lg-7"/>
            <logical_group_ref idref="lg-5"/>
         </contents>
      </load_segment>
      <load_segment id="lg-18d" display="no" color="cyan">
         <name>SEGMENT_1</name>
         <run_address>0x20200000</run_address>
         <size>0x25</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-e"/>
            <logical_group_ref idref="lg-f"/>
         </contents>
      </load_segment>
      <load_segment id="lg-18e" display="no" color="cyan">
         <name>SEGMENT_2</name>
         <run_address>0x20207e00</run_address>
         <size>0x200</size>
         <flags>0x6</flags>
         <contents>
            <logical_group_ref idref="lg-11"/>
         </contents>
      </load_segment>
   </logical_group_list>
   <placement_map>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x14d0</used_space>
         <unused_space>0x1eb30</unused_space>
         <attributes>RX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-a"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x0</start_address>
               <size>0xc0</size>
               <logical_group_ref idref="lg-2"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc0</start_address>
               <size>0xba8</size>
               <logical_group_ref idref="lg-3"/>
            </allocated_space>
            <allocated_space>
               <start_address>0xc68</start_address>
               <size>0x830</size>
               <logical_group_ref idref="lg-7"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x1498</start_address>
               <size>0x38</size>
               <logical_group_ref idref="lg-5"/>
            </allocated_space>
            <available_space>
               <start_address>0x14d0</start_address>
               <size>0x1eb30</size>
            </available_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>FLASH</name>
         <page_id>0x0</page_id>
         <origin>0x0</origin>
         <length>0x20000</length>
         <used_space>0x0</used_space>
         <unused_space>0x20000</unused_space>
         <attributes>RX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x222</used_space>
         <unused_space>0x7dde</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-132"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x0</size>
               <logical_group_ref idref="lg-134"/>
            </allocated_space>
            <allocated_space>
               <start_address>0x20200000</start_address>
               <size>0x1d</size>
               <logical_group_ref idref="lg-e"/>
            </allocated_space>
            <available_space>
               <start_address>0x2020001d</start_address>
               <size>0x3</size>
            </available_space>
            <allocated_space>
               <start_address>0x20200020</start_address>
               <size>0x5</size>
               <logical_group_ref idref="lg-f"/>
            </allocated_space>
            <available_space>
               <start_address>0x20200025</start_address>
               <size>0x7ddb</size>
            </available_space>
            <allocated_space>
               <start_address>0x20207e00</start_address>
               <size>0x200</size>
               <logical_group_ref idref="lg-11"/>
            </allocated_space>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>SRAM</name>
         <page_id>0x0</page_id>
         <origin>0x20200000</origin>
         <length>0x8000</length>
         <used_space>0x0</used_space>
         <unused_space>0x8000</unused_space>
         <attributes>RWX</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BCR_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00000</origin>
         <length>0xff</length>
         <used_space>0x0</used_space>
         <unused_space>0xff</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
      <memory_area display="yes" color="green">
         <name>BSL_CONFIG</name>
         <page_id>0x0</page_id>
         <origin>0x41c00100</origin>
         <length>0x80</length>
         <used_space>0x0</used_space>
         <unused_space>0x80</unused_space>
         <attributes>R</attributes>
         <usage_details>
         </usage_details>
      </memory_area>
   </placement_map>
   <cptbl_list>
      <cptbl>
         <name>__TI_cinit_table</name>
         <cprec>
            <name>.data</name>
            <load_address>0x1498</load_address>
            <load_size>0x13</load_size>
            <run_address>0x20200000</run_address>
            <run_size>0x1d</run_size>
            <compression>lzss</compression>
         </cprec>
         <cprec>
            <name>.bss</name>
            <load_address>0x14b8</load_address>
            <load_size>0x8</load_size>
            <run_address>0x20200020</run_address>
            <run_size>0x5</run_size>
            <compression>zero_init</compression>
         </cprec>
      </cptbl>
   </cptbl_list>
   <handler_table>
      <handler_table_name>__TI_handler_table</handler_table_name>
      <handler>
         <index>0x0</index>
         <name>__TI_decompress_lzss</name>
      </handler>
      <handler>
         <index>0x1</index>
         <name>__TI_decompress_none</name>
      </handler>
      <handler>
         <index>0x2</index>
         <name>__TI_zero_init</name>
      </handler>
   </handler_table>
   <symbol_table>
      <symbol id="sm-1">
         <name>__start___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-2">
         <name>__stop___llvm_prf_cnts</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-3">
         <name>__start___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-4">
         <name>__stop___llvm_prf_bits</name>
         <value>0x20200000</value>
      </symbol>
      <symbol id="sm-5">
         <name>__TI_CINIT_Base</name>
         <value>0x14c0</value>
      </symbol>
      <symbol id="sm-6">
         <name>__TI_CINIT_Limit</name>
         <value>0x14d0</value>
      </symbol>
      <symbol id="sm-7">
         <name>__TI_CINIT_Warm</name>
         <value>0x14d0</value>
      </symbol>
      <symbol id="sm-8">
         <name>__TI_Handler_Table_Base</name>
         <value>0x14ac</value>
      </symbol>
      <symbol id="sm-9">
         <name>__TI_Handler_Table_Limit</name>
         <value>0x14b8</value>
      </symbol>
      <symbol id="sm-a">
         <name>binit</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-b">
         <name>__binit__</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-c">
         <name>__STACK_SIZE</name>
         <value>0x200</value>
      </symbol>
      <symbol id="sm-d">
         <name>__STACK_END</name>
         <value>0x20208000</value>
      </symbol>
      <symbol id="sm-e">
         <name>__TI_pprof_out_hndl</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-f">
         <name>__TI_prof_data_start</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-10">
         <name>__TI_prof_data_size</name>
         <value>0xffffffff</value>
      </symbol>
      <symbol id="sm-52">
         <name>led_blink_task</name>
         <value>0xc35</value>
         <object_component_ref idref="oc-104"/>
      </symbol>
      <symbol id="sm-53">
         <name>main</name>
         <value>0x9d5</value>
         <object_component_ref idref="oc-7d"/>
      </symbol>
      <symbol id="sm-6e">
         <name>SYSCFG_DL_init</name>
         <value>0xba3</value>
         <object_component_ref idref="oc-9c"/>
      </symbol>
      <symbol id="sm-6f">
         <name>SYSCFG_DL_initPower</name>
         <value>0xabd</value>
         <object_component_ref idref="oc-d4"/>
      </symbol>
      <symbol id="sm-70">
         <name>SYSCFG_DL_GPIO_init</name>
         <value>0xa89</value>
         <object_component_ref idref="oc-d5"/>
      </symbol>
      <symbol id="sm-71">
         <name>SYSCFG_DL_SYSCTL_init</name>
         <value>0x955</value>
         <object_component_ref idref="oc-d6"/>
      </symbol>
      <symbol id="sm-72">
         <name>SYSCFG_DL_I2C_OLED_init</name>
         <value>0x8bd</value>
         <object_component_ref idref="oc-d8"/>
      </symbol>
      <symbol id="sm-73">
         <name>SYSCFG_DL_SYSTICK_init</name>
         <value>0xb25</value>
         <object_component_ref idref="oc-d9"/>
      </symbol>
      <symbol id="sm-7e">
         <name>Default_Handler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-7f">
         <name>Reset_Handler</name>
         <value>0xc5f</value>
         <object_component_ref idref="oc-41"/>
      </symbol>
      <symbol id="sm-80">
         <name>interruptVectors</name>
         <value>0x0</value>
         <object_component_ref idref="oc-2a"/>
      </symbol>
      <symbol id="sm-81">
         <name>NMI_Handler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-82">
         <name>HardFault_Handler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-83">
         <name>SVC_Handler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-84">
         <name>PendSV_Handler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-85">
         <name>GROUP0_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-86">
         <name>GROUP1_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-87">
         <name>TIMG8_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-88">
         <name>UART3_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-89">
         <name>ADC0_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-8a">
         <name>ADC1_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-8b">
         <name>CANFD0_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-8c">
         <name>DAC0_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-8d">
         <name>SPI0_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-8e">
         <name>SPI1_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-8f">
         <name>UART1_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-90">
         <name>UART2_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-91">
         <name>UART0_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-92">
         <name>TIMG0_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-93">
         <name>TIMG6_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-94">
         <name>TIMA0_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-95">
         <name>TIMA1_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-96">
         <name>TIMG7_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-97">
         <name>TIMG12_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-98">
         <name>I2C0_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-99">
         <name>I2C1_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-9a">
         <name>AES_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-9b">
         <name>RTC_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-9c">
         <name>DMA_IRQHandler</name>
         <value>0x767</value>
         <object_component_ref idref="oc-45"/>
      </symbol>
      <symbol id="sm-ac">
         <name>scheduler_init</name>
         <value>0xbed</value>
         <object_component_ref idref="oc-a1"/>
      </symbol>
      <symbol id="sm-ad">
         <name>task_num</name>
         <value>0x20200024</value>
      </symbol>
      <symbol id="sm-ae">
         <name>scheduler_run</name>
         <value>0x911</value>
         <object_component_ref idref="oc-b0"/>
      </symbol>
      <symbol id="sm-bb">
         <name>oled_task</name>
         <value>0xa11</value>
         <object_component_ref idref="oc-105"/>
      </symbol>
      <symbol id="sm-d0">
         <name>OLED_WR_Byte</name>
         <value>0x4bd</value>
         <object_component_ref idref="oc-e5"/>
      </symbol>
      <symbol id="sm-d1">
         <name>OLED_Clear</name>
         <value>0x379</value>
         <object_component_ref idref="oc-116"/>
      </symbol>
      <symbol id="sm-d2">
         <name>OLED_ShowChar</name>
         <value>0x5f1</value>
         <object_component_ref idref="oc-11d"/>
      </symbol>
      <symbol id="sm-d3">
         <name>asc2_1608</name>
         <value>0xc68</value>
         <object_component_ref idref="oc-122"/>
      </symbol>
      <symbol id="sm-d4">
         <name>asc2_0806</name>
         <value>0x1258</value>
         <object_component_ref idref="oc-123"/>
      </symbol>
      <symbol id="sm-d5">
         <name>OLED_ShowString</name>
         <value>0x7e5</value>
         <object_component_ref idref="oc-117"/>
      </symbol>
      <symbol id="sm-d6">
         <name>OLED_Init</name>
         <value>0xc1</value>
         <object_component_ref idref="oc-aa"/>
      </symbol>
      <symbol id="sm-e7">
         <name>mspm0_delay_ms</name>
         <value>0xaf1</value>
         <object_component_ref idref="oc-e4"/>
      </symbol>
      <symbol id="sm-e8">
         <name>sys_tick</name>
         <value>0x20200018</value>
         <object_component_ref idref="oc-6c"/>
      </symbol>
      <symbol id="sm-e9">
         <name>start_time</name>
         <value>0x20200020</value>
      </symbol>
      <symbol id="sm-ea">
         <name>mspm0_get_clock_ms</name>
         <value>0xbbd</value>
         <object_component_ref idref="oc-102"/>
      </symbol>
      <symbol id="sm-eb">
         <name>SysTick_Init</name>
         <value>0xc25</value>
         <object_component_ref idref="oc-dc"/>
      </symbol>
      <symbol id="sm-f4">
         <name>SysTick_Handler</name>
         <value>0xc15</value>
         <object_component_ref idref="oc-46"/>
      </symbol>
      <symbol id="sm-f5">
         <name>__TI_ATRegion0_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f6">
         <name>__TI_ATRegion0_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f7">
         <name>__TI_ATRegion0_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f8">
         <name>__TI_ATRegion1_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-f9">
         <name>__TI_ATRegion1_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-fa">
         <name>__TI_ATRegion1_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-fb">
         <name>__TI_ATRegion2_src_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-fc">
         <name>__TI_ATRegion2_trg_addr</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-fd">
         <name>__TI_ATRegion2_region_sz</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-106">
         <name>DL_Common_delayCycles</name>
         <value>0xc41</value>
         <object_component_ref idref="oc-a6"/>
      </symbol>
      <symbol id="sm-112">
         <name>DL_I2C_setClockConfig</name>
         <value>0xb7d</value>
         <object_component_ref idref="oc-fa"/>
      </symbol>
      <symbol id="sm-113">
         <name>DL_I2C_fillControllerTXFIFO</name>
         <value>0x85f</value>
         <object_component_ref idref="oc-103"/>
      </symbol>
      <symbol id="sm-11e">
         <name>_c_int00_noargs</name>
         <value>0xb55</value>
         <object_component_ref idref="oc-65"/>
      </symbol>
      <symbol id="sm-11f">
         <name>__stack</name>
         <value>0x20207e00</value>
         <object_component_ref idref="oc-2c"/>
      </symbol>
      <symbol id="sm-12b">
         <name>__TI_auto_init_nobinit_nopinit</name>
         <value>0xa4d</value>
         <object_component_ref idref="oc-bb"/>
      </symbol>
      <symbol id="sm-133">
         <name>_system_pre_init</name>
         <value>0xc63</value>
         <object_component_ref idref="oc-79"/>
      </symbol>
      <symbol id="sm-13e">
         <name>__TI_zero_init_nomemset</name>
         <value>0xbd5</value>
         <object_component_ref idref="oc-5c"/>
      </symbol>
      <symbol id="sm-147">
         <name>__TI_decompress_none</name>
         <value>0xc01</value>
         <object_component_ref idref="oc-32"/>
      </symbol>
      <symbol id="sm-152">
         <name>__TI_decompress_lzss</name>
         <value>0x769</value>
         <object_component_ref idref="oc-2e"/>
      </symbol>
      <symbol id="sm-15c">
         <name>abort</name>
         <value>0xc55</value>
         <object_component_ref idref="oc-b4"/>
      </symbol>
      <symbol id="sm-166">
         <name>HOSTexit</name>
         <value>0xc5b</value>
         <object_component_ref idref="oc-e9"/>
      </symbol>
      <symbol id="sm-167">
         <name>C$$EXIT</name>
         <value>0xc5a</value>
         <object_component_ref idref="oc-e9"/>
      </symbol>
      <symbol id="sm-16d">
         <name>__aeabi_memcpy</name>
         <value>0xc4d</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-16e">
         <name>__aeabi_memcpy4</name>
         <value>0xc4d</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-16f">
         <name>__aeabi_memcpy8</name>
         <value>0xc4d</value>
         <object_component_ref idref="oc-55"/>
      </symbol>
      <symbol id="sm-175">
         <name>__aeabi_uidiv</name>
         <value>0x995</value>
         <object_component_ref idref="oc-11e"/>
      </symbol>
      <symbol id="sm-176">
         <name>__aeabi_uidivmod</name>
         <value>0x995</value>
         <object_component_ref idref="oc-11e"/>
      </symbol>
      <symbol id="sm-180">
         <name>__aeabi_idiv0</name>
         <value>0xbbb</value>
         <object_component_ref idref="oc-124"/>
      </symbol>
      <symbol id="sm-199">
         <name>memcpy</name>
         <value>0x6cd</value>
         <object_component_ref idref="oc-97"/>
      </symbol>
      <symbol id="sm-19a">
         <name>__TI_static_base__</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-19d">
         <name>__mpu_init</name>
         <value>0x0</value>
      </symbol>
      <symbol id="sm-19e">
         <name>_system_post_cinit</name>
         <value>0x0</value>
      </symbol>
   </symbol_table>
   <title>Link errors: red sections failed placement</title>
</link_info>
