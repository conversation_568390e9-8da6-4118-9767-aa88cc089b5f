# MPU6050移植问题清单和API文档

## 问题清单

### 已修复的问题

#### 1. 编译宏定义缺失 ✅
**问题描述**: `inv_mpu.c`文件中缺少平台相关的宏定义，导致编译时无法识别目标平台。

**原因分析**: 
- 缺少`MOTION_DRIVER_TARGET_MSPM0`宏定义
- 缺少`MPU6050`芯片类型宏定义

**解决方案**: 
在`BSP/MPU6050/inv_mpu.h`文件开头添加：
```c
/* Define platform and MPU type for MSPM0 */
#ifndef MOTION_DRIVER_TARGET_MSPM0
#define MOTION_DRIVER_TARGET_MSPM0
#endif

#ifndef MPU6050
#define MPU6050
#endif
```

#### 2. GPIO中断配置问题 ✅
**问题描述**: `mpu6050.c`中使用了`GPIO_MPU6050_INT_IRQN`但该宏未定义。

**原因分析**: 
- 中断引脚配置已在`ti_msp_dl_config.h`中定义
- 代码中缺少相应的注释说明

**解决方案**: 
- 确认`ti_msp_dl_config.h`中已正确定义`GPIO_MPU6050_INT_IRQN`
- 在代码中添加注释说明中断配置

#### 3. 数学库函数缺失 ✅
**问题描述**: `mpu6050.c`中使用了`asin`、`atan2`等数学函数但未包含数学库头文件。

**解决方案**: 
在`BSP/MPU6050/mpu6050.c`中添加：
```c
#include <math.h>
```

#### 4. 应用层代码缺失 ✅
**问题描述**: `APP/mpu6050_app.c`文件几乎为空，缺少应用层接口函数。

**解决方案**: 
- 实现了完整的MPU6050任务函数
- 添加了数据状态管理函数
- 提供了数据获取接口函数

#### 5. 主程序集成缺失 ✅
**问题描述**: 主程序中未调用MPU6050初始化和任务函数。

**解决方案**: 
- 在`main()`函数中添加`MPU6050_Init()`调用
- 在任务调度器中添加`mpu6050_task`

### 潜在问题和注意事项

#### 1. I2C总线冲突 ⚠️
**问题描述**: MPU6050和OLED都使用I2C接口，可能存在总线冲突。

**当前状态**: 
- MPU6050使用I2C0 (PA0-SDA, PA1-SCL)
- OLED使用I2C1 (PA10-SDA, PA11-SCL)
- 硬件上已分离，无冲突

#### 2. 中断处理函数缺失 ⚠️
**问题描述**: 虽然启用了MPU6050中断，但未实现中断处理函数。

**建议**: 
在`System/interrupt.c`中添加MPU6050中断处理函数：
```c
void GPIOB_IRQHandler(void)
{
    switch (DL_Interrupt_getPendingGroup(DL_INTERRUPT_GROUP_1)) {
        case GPIO_MPU6050_INT_IIDX:
            // MPU6050中断处理
            break;
    }
}
```

#### 3. DMP固件加载时间 ⚠️
**问题描述**: DMP固件加载可能需要较长时间，可能影响系统启动。

**建议**: 
- 考虑在初始化时添加超时机制
- 添加初始化状态指示

## MPU6050 API函数说明

### 底层驱动函数 (BSP/MPU6050/)

#### 1. MPU6050_Init()
```c
void MPU6050_Init(void);
```
**功能**: 初始化MPU6050传感器
**参数**: 无
**返回值**: 无
**说明**: 
- 配置I2C接口
- 初始化MPU6050寄存器
- 加载DMP固件
- 配置传感器参数
- 启用中断

#### 2. Read_Quad()
```c
int Read_Quad(void);
```
**功能**: 读取四元数数据并计算姿态角
**参数**: 无
**返回值**: 
- 0: 成功
- -1: 失败
**说明**: 
- 从FIFO读取DMP数据
- 计算pitch、roll、yaw角度
- 更新全局变量

### 应用层函数 (APP/mpu6050_app.c)

#### 1. mpu6050_task()
```c
void mpu6050_task(void);
```
**功能**: MPU6050数据更新任务
**参数**: 无
**返回值**: 无
**说明**: 
- 每20ms执行一次 (50Hz)
- 自动读取MPU6050数据
- 设置数据就绪标志

#### 2. mpu6050_is_data_ready()
```c
bool mpu6050_is_data_ready(void);
```
**功能**: 检查MPU6050数据是否就绪
**参数**: 无
**返回值**: 
- true: 数据就绪
- false: 数据未就绪

#### 3. mpu6050_clear_data_ready()
```c
void mpu6050_clear_data_ready(void);
```
**功能**: 清除数据就绪标志
**参数**: 无
**返回值**: 无

#### 4. mpu6050_get_angles()
```c
void mpu6050_get_angles(float *pitch_val, float *roll_val, float *yaw_val);
```
**功能**: 获取姿态角度数据
**参数**: 
- pitch_val: 俯仰角指针 (度)
- roll_val: 横滚角指针 (度)  
- yaw_val: 偏航角指针 (度)
**返回值**: 无
**说明**: 参数可以为NULL，表示不需要该数据

#### 5. mpu6050_get_gyro_data()
```c
void mpu6050_get_gyro_data(short *gyro_x, short *gyro_y, short *gyro_z);
```
**功能**: 获取原始陀螺仪数据
**参数**: 
- gyro_x: X轴陀螺仪数据指针
- gyro_y: Y轴陀螺仪数据指针
- gyro_z: Z轴陀螺仪数据指针
**返回值**: 无

#### 6. mpu6050_get_accel_data()
```c
void mpu6050_get_accel_data(short *accel_x, short *accel_y, short *accel_z);
```
**功能**: 获取原始加速度计数据
**参数**: 
- accel_x: X轴加速度数据指针
- accel_y: Y轴加速度数据指针
- accel_z: Z轴加速度数据指针
**返回值**: 无

### I2C底层函数 (BSP/MPU6050/mspm0_i2c.c)

#### 1. mspm0_i2c_write()
```c
int mspm0_i2c_write(unsigned char slave_addr, unsigned char reg_addr, 
                    unsigned char length, unsigned char const *data);
```
**功能**: I2C写数据
**参数**: 
- slave_addr: 从设备地址
- reg_addr: 寄存器地址
- length: 数据长度
- data: 数据指针
**返回值**: 
- 0: 成功
- -1: 失败

#### 2. mspm0_i2c_read()
```c
int mspm0_i2c_read(unsigned char slave_addr, unsigned char reg_addr,
                   unsigned char length, unsigned char *data);
```
**功能**: I2C读数据
**参数**: 
- slave_addr: 从设备地址
- reg_addr: 寄存器地址
- length: 数据长度
- data: 数据缓冲区指针
**返回值**: 
- 0: 成功
- -1: 失败

#### 3. mpu6050_i2c_sda_unlock()
```c
void mpu6050_i2c_sda_unlock(void);
```
**功能**: I2C总线解锁
**参数**: 无
**返回值**: 无
**说明**: 当I2C总线卡死时使用

## 使用示例

### 基本使用流程
```c
#include "mpu6050_app.h"

int main(void)
{
    // 系统初始化
    SYSCFG_DL_init();
    scheduler_init();
    
    // MPU6050初始化
    MPU6050_Init();
    
    while(1)
    {
        scheduler_run();
        
        // 检查MPU6050数据
        if(mpu6050_is_data_ready())
        {
            float pitch, roll, yaw;
            mpu6050_get_angles(&pitch, &roll, &yaw);
            
            // 处理角度数据
            printf("Pitch: %.2f, Roll: %.2f, Yaw: %.2f\n", pitch, roll, yaw);
            
            mpu6050_clear_data_ready();
        }
    }
}
```

### 获取原始传感器数据
```c
if(mpu6050_is_data_ready())
{
    short gyro_x, gyro_y, gyro_z;
    short accel_x, accel_y, accel_z;
    
    mpu6050_get_gyro_data(&gyro_x, &gyro_y, &gyro_z);
    mpu6050_get_accel_data(&accel_x, &accel_y, &accel_z);
    
    // 处理原始数据
    printf("Gyro: %d, %d, %d\n", gyro_x, gyro_y, gyro_z);
    printf("Accel: %d, %d, %d\n", accel_x, accel_y, accel_z);
    
    mpu6050_clear_data_ready();
}
```

## 配置参数

### 硬件连接
- **I2C接口**: I2C0
- **SDA引脚**: PA0 (IOMUX_PINCM1)
- **SCL引脚**: PA1 (IOMUX_PINCM2)
- **中断引脚**: PB13 (IOMUX_PINCM30)
- **I2C速度**: 400kHz

### 软件配置
- **采样率**: 50Hz (DEFAULT_MPU_HZ)
- **DMP特性**: 6轴四元数、点击检测、方向检测、陀螺仪校准
- **任务周期**: 20ms
- **FIFO配置**: 陀螺仪 + 加速度计数据

## 注意事项

1. **初始化顺序**: 必须先初始化系统时钟和I2C，再初始化MPU6050
2. **数据同步**: 使用数据就绪标志确保数据同步
3. **错误处理**: 初始化失败时会触发系统复位
4. **中断处理**: 当前版本未实现中断处理函数，依赖轮询方式
5. **总线冲突**: 确保I2C总线不与其他设备冲突
6. **电源管理**: MPU6050初始化时会配置电源管理寄存器

## 测试示例

### 在OLED上显示MPU6050数据
```c
// 在oled_app.c中添加MPU6050数据显示
void oled_task(void)
{
    static uint32_t last_display_time = 0;
    uint32_t current_time = SystemTime_GetMs();

    // 每100ms更新一次显示
    if (current_time - last_display_time >= 100)
    {
        if (mpu6050_is_data_ready())
        {
            float pitch, roll, yaw;
            mpu6050_get_angles(&pitch, &roll, &yaw);

            char buffer[32];

            // 清屏
            OLED_Clear();

            // 显示角度数据
            sprintf(buffer, "Pitch: %.1f", pitch);
            OLED_ShowString(0, 0, buffer, 12);

            sprintf(buffer, "Roll:  %.1f", roll);
            OLED_ShowString(0, 16, buffer, 12);

            sprintf(buffer, "Yaw:   %.1f", yaw);
            OLED_ShowString(0, 32, buffer, 12);

            OLED_Refresh();
            mpu6050_clear_data_ready();
        }
        last_display_time = current_time;
    }
}
```

### LED指示MPU6050状态
```c
// 修改led_blink_task来指示MPU6050状态
void led_blink_task(void)
{
    static bool mpu6050_ok = false;

    // 检查MPU6050是否有数据更新
    if (mpu6050_is_data_ready())
    {
        mpu6050_ok = true;
        // 快速闪烁表示MPU6050正常工作
        DL_GPIO_togglePins(Led_PORT, Led_PIN_A3_PIN);
    }
    else if (!mpu6050_ok)
    {
        // 慢速闪烁表示MPU6050未初始化或无数据
        DL_GPIO_togglePins(Led_PORT, Led_PIN_A3_PIN);
    }
}
```

## 故障排除

### 常见问题
1. **初始化失败**: 检查I2C连接和电源
2. **数据不更新**: 检查任务调度器是否正常运行
3. **角度计算异常**: 检查DMP固件是否正确加载
4. **I2C通信失败**: 使用`mpu6050_i2c_sda_unlock()`解锁总线
5. **编译错误**: 确保所有宏定义正确，包含必要的头文件

### 调试步骤
1. **检查硬件连接**:
   - VCC -> 3.3V
   - GND -> GND
   - SDA -> PA0
   - SCL -> PA1
   - INT -> PB13 (可选)

2. **检查I2C通信**:
   ```c
   // 在MPU6050_Init()后添加测试代码
   unsigned char who_am_i;
   if (mspm0_i2c_read(0x68, 0x75, 1, &who_am_i) == 0)
   {
       if (who_am_i == 0x68)
           printf("MPU6050 detected successfully\n");
       else
           printf("Wrong device ID: 0x%02X\n", who_am_i);
   }
   else
   {
       printf("I2C communication failed\n");
   }
   ```

3. **检查DMP状态**:
   ```c
   unsigned char dmp_enabled;
   mpu_get_dmp_state(&dmp_enabled);
   printf("DMP enabled: %d\n", dmp_enabled);
   ```

### 性能优化建议
1. **降低采样率**: 如果不需要高频数据，可以降低采样率以节省CPU资源
2. **选择性读取**: 根据应用需求选择读取角度数据或原始数据
3. **中断驱动**: 使用中断方式可以提高响应速度，减少轮询开销
4. **数据滤波**: 对角度数据进行滤波处理以减少噪声
