/*
 * Copyright (c) 2021, Texas Instruments Incorporated
 * All rights reserved.
 *
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions
 * are met:
 *
 * *  Redistributions of source code must retain the above copyright
 *    notice, this list of conditions and the following disclaimer.
 *
 * *  Redistributions in binary form must reproduce the above copyright
 *    notice, this list of conditions and the following disclaimer in the
 *    documentation and/or other materials provided with the distribution.
 *
 * *  Neither the name of Texas Instruments Incorporated nor the names of
 *    its contributors may be used to endorse or promote products derived
 *    from this software without specific prior written permission.
 *
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS"
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
 * THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
 * PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR
 * CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, <PERSON>ECIAL,
 * EXEMPLARY, OR <PERSON>NS<PERSON>QUENTIAL DAMAGES (INCLUDING, BUT NOT <PERSON>IMITED TO,
 * PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS;
 * OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY,
 * WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR
 * OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE,
 * EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 */

#include "mydefine.h"

// LED闪烁任务函数
void led_blink_task(void);

// LED闪烁任务实现
void led_blink_task(void)
{
    DL_GPIO_togglePins(Led_PORT, Led_PIN_A3_PIN);
}

int main(void)
{
    // 系统初始化（现在包括SysTick配置）
    SYSCFG_DL_init();

    // 初始化任务调度器（现在只设置任务表和启用全局中断）
    scheduler_init();

    // 等待SysTick稳定运行（约10ms）
    delay_cycles(320000);  // 32MHz时钟，10ms延时

    // 测试SysTick是否工作：检查sys_tick是否在递增
    extern volatile uint32_t sys_tick;
    uint32_t test_start = sys_tick;
    delay_cycles(32000);  // 等待约1ms
    uint32_t test_end = sys_tick;

    // 如果SysTick工作正常，test_end应该大于test_start
    // 如果不工作，两个值会相等
    if (test_end > test_start) {
        // SysTick工作正常，初始化OLED
        OLED_Init();
    } else {
        // SysTick不工作，只闪烁LED作为指示
        // LED会以最快速度闪烁表示SysTick故障
        // 在这种情况下，调度器仍然会运行，但时间基准不准确
    }

    while (1)
    {
        // 运行任务调度器
        scheduler_run();
    }
}
